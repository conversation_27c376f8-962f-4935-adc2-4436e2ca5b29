#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版加密货币量化交易回测系统

基于507个USDT永续合约日K线实体涨跌幅数据，使用LightGBM模型，
构建完整的量化交易回测系统。

核心功能：
- 基于实体涨跌幅的技术指标特征
- LightGBM滚动训练预测
- T+2日预测策略（防未来函数）
- 多空选币策略（前5做多，后5做空）
- 完整的回测框架和性能分析

作者：加密货币量化交易系统
日期：2025年1月29日
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import os
import json
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
import sys

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimplifiedCryptoBacktest:
    """简化版加密货币量化交易回测系统"""
    
    def __init__(self, 
                 data_dir: str = "daily_body_changes",
                 output_dir: str = "simplified_backtest_results",
                 start_date: str = "2022-01-01",
                 end_date: str = "2025-07-29"):
        """
        初始化回测系统
        
        Args:
            data_dir: 日K线实体涨跌幅数据目录
            output_dir: 回测结果输出目录
            start_date: 回测开始日期
            end_date: 回测结束日期
        """
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "reports"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "charts"), exist_ok=True)
        
        # 策略参数
        self.long_positions = 5  # 做多币种数量
        self.short_positions = 5  # 做空币种数量
        self.prediction_horizon = 2  # T+2预测
        
        # 交易成本参数
        self.transaction_cost = 0.001  # 0.1%交易成本
        
        # 数据存储
        self.data = None
        self.predictions = []
        self.portfolio_records = []
        
        # 模型参数
        self.lgb_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }
        
        print("🚀 简化版加密货币量化交易回测系统初始化完成")
        print("=" * 60)
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📅 回测期间: {start_date} ~ {end_date}")
        print(f"🎯 策略配置: 做多{self.long_positions}个，做空{self.short_positions}个")
        print(f"⏰ T+{self.prediction_horizon}预测策略")
    
    def load_data(self) -> bool:
        """
        加载507个合约的数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            print("\n📊 加载507个合约数据...")
            
            combined_file = os.path.join(self.data_dir, "combined_data", "all_contracts_body_changes.csv")
            
            if not os.path.exists(combined_file):
                print(f"❌ 合并数据文件不存在: {combined_file}")
                return False
            
            # 读取合并数据
            self.data = pd.read_csv(combined_file)
            self.data['date'] = pd.to_datetime(self.data['date'])
            
            # 筛选时间范围
            self.data = self.data[(self.data['date'] >= self.start_date) & (self.data['date'] <= self.end_date)]
            
            if self.data.empty:
                print(f"❌ 指定时间范围内无数据")
                return False
            
            # 按日期排序
            self.data = self.data.sort_values(['date', 'symbol']).reset_index(drop=True)
            
            print(f"✅ 成功加载数据")
            print(f"📊 总样本数: {len(self.data):,}")
            print(f"🪙 币种数量: {self.data['symbol'].nunique()}")
            print(f"📅 时间范围: {self.data['date'].min()} ~ {self.data['date'].max()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {str(e)}")
            return False
    
    def create_features(self) -> bool:
        """
        创建技术指标特征
        
        Returns:
            bool: 特征创建是否成功
        """
        try:
            print("\n🔧 创建技术指标特征...")
            
            feature_data = []
            
            for symbol in self.data['symbol'].unique():
                symbol_data = self.data[self.data['symbol'] == symbol].copy()
                symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
                
                if len(symbol_data) < 30:  # 至少需要30天数据
                    continue
                
                # 计算技术指标特征
                for period in [5, 10, 20]:
                    # 实体涨跌幅移动平均
                    symbol_data[f'body_ma_{period}'] = symbol_data['body_change_pct'].rolling(period).mean()
                    symbol_data[f'body_std_{period}'] = symbol_data['body_change_pct'].rolling(period).std()
                    
                    # 价格移动平均
                    symbol_data[f'close_ma_{period}'] = symbol_data['close'].rolling(period).mean()
                    symbol_data[f'close_ratio_{period}'] = symbol_data['close'] / symbol_data[f'close_ma_{period}'] - 1
                    
                    # 收益率
                    symbol_data[f'return_{period}'] = symbol_data['close'].pct_change(period)
                    
                    # 波动率
                    symbol_data[f'volatility_{period}'] = symbol_data['close'].pct_change().rolling(period).std()
                
                # RSI指标
                delta = symbol_data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
                rs = gain / loss
                symbol_data['rsi_14'] = 100 - (100 / (1 + rs))
                
                # 布林带位置
                ma20 = symbol_data['close'].rolling(20).mean()
                std20 = symbol_data['close'].rolling(20).std()
                symbol_data['bb_position'] = (symbol_data['close'] - ma20) / (2 * std20)
                
                # 创建目标变量（T+2日实体涨跌幅）
                symbol_data['target'] = symbol_data['body_change_pct'].shift(-self.prediction_horizon)
                
                # 删除无效数据
                symbol_data = symbol_data.dropna()
                
                if len(symbol_data) > 0:
                    feature_data.append(symbol_data)
            
            if not feature_data:
                print("❌ 没有有效的特征数据")
                return False
            
            # 合并所有特征数据
            self.data = pd.concat(feature_data, ignore_index=True)
            
            # 获取特征列
            exclude_cols = ['timestamp', 'date', 'symbol', 'target', 'candle_type']
            self.feature_columns = [col for col in self.data.columns 
                                  if col not in exclude_cols and not col.startswith('open') 
                                  and not col.startswith('high') and not col.startswith('low')]
            
            print(f"✅ 特征创建完成")
            print(f"📊 有效样本数: {len(self.data):,}")
            print(f"🔢 特征数量: {len(self.feature_columns)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建特征失败: {str(e)}")
            return False
    
    def rolling_prediction(self) -> bool:
        """
        滚动训练和预测
        
        Returns:
            bool: 预测是否成功
        """
        try:
            print("\n🤖 开始滚动训练和预测...")
            
            # 获取所有交易日期
            all_dates = sorted(self.data['date'].unique())
            min_train_days = 60
            
            for i, current_date in enumerate(all_dates[min_train_days:], min_train_days):
                if i % 30 == 0:  # 每30天显示一次进度
                    print(f"  处理进度: {i-min_train_days+1}/{len(all_dates)-min_train_days} ({(i-min_train_days+1)/(len(all_dates)-min_train_days)*100:.1f}%)")
                
                # 准备训练数据
                train_data = self.data[self.data['date'] < current_date].copy()
                pred_data = self.data[self.data['date'] == current_date].copy()
                
                if len(train_data) < min_train_days or len(pred_data) == 0:
                    continue
                
                # 特征和目标变量
                X_train = train_data[self.feature_columns].fillna(0)
                y_train = train_data['target'].fillna(0)
                X_pred = pred_data[self.feature_columns].fillna(0)
                
                # 训练模型
                train_dataset = lgb.Dataset(X_train, label=y_train)
                model = lgb.train(
                    self.lgb_params,
                    train_dataset,
                    num_boost_round=50,
                    callbacks=[lgb.log_evaluation(0)]
                )
                
                # 预测
                y_pred = model.predict(X_pred)
                
                # 保存预测结果
                for j, (idx, row) in enumerate(pred_data.iterrows()):
                    self.predictions.append({
                        'date': current_date,
                        'symbol': row['symbol'],
                        'predicted_return': y_pred[j],
                        'actual_return': row['target']
                    })
            
            print(f"✅ 滚动预测完成，生成 {len(self.predictions):,} 个预测")
            
            return True

        except Exception as e:
            print(f"❌ 滚动预测失败: {str(e)}")
            return False

    def execute_strategy(self) -> bool:
        """
        执行交易策略

        Returns:
            bool: 策略执行是否成功
        """
        try:
            print("\n💼 执行交易策略...")

            # 转换预测为DataFrame
            pred_df = pd.DataFrame(self.predictions)

            if pred_df.empty:
                print("❌ 没有预测数据")
                return False

            # 按日期分组执行策略
            trading_dates = sorted(pred_df['date'].unique())
            current_positions = {}

            for trade_date in trading_dates:
                daily_pred = pred_df[pred_df['date'] == trade_date].copy()

                if len(daily_pred) < self.long_positions + self.short_positions:
                    continue

                # 按预测收益率排序
                daily_pred = daily_pred.sort_values('predicted_return', ascending=False)

                # 选择做多和做空的币种
                long_symbols = daily_pred.head(self.long_positions)['symbol'].tolist()
                short_symbols = daily_pred.tail(self.short_positions)['symbol'].tolist()

                # 计算当日收益
                daily_return = 0.0
                transaction_costs = 0.0

                # 计算持仓收益
                for symbol, weight in current_positions.items():
                    symbol_data = daily_pred[daily_pred['symbol'] == symbol]
                    if not symbol_data.empty and not pd.isna(symbol_data['actual_return'].iloc[0]):
                        actual_return = symbol_data['actual_return'].iloc[0]
                        daily_return += weight * actual_return / 100

                # 更新持仓
                new_positions = {}
                long_weight = 1.0 / self.long_positions if self.long_positions > 0 else 0
                short_weight = -1.0 / self.short_positions if self.short_positions > 0 else 0

                for symbol in long_symbols:
                    new_positions[symbol] = long_weight
                for symbol in short_symbols:
                    new_positions[symbol] = short_weight

                # 计算调仓成本
                all_symbols = set(list(current_positions.keys()) + list(new_positions.keys()))
                for symbol in all_symbols:
                    old_weight = current_positions.get(symbol, 0)
                    new_weight = new_positions.get(symbol, 0)
                    if abs(new_weight - old_weight) > 1e-6:
                        transaction_costs += abs(new_weight - old_weight) * self.transaction_cost

                current_positions = new_positions.copy()

                # 记录组合表现
                self.portfolio_records.append({
                    'date': trade_date,
                    'gross_return': daily_return,
                    'transaction_cost': transaction_costs,
                    'net_return': daily_return - transaction_costs,
                    'long_symbols': ','.join(long_symbols),
                    'short_symbols': ','.join(short_symbols)
                })

            print(f"✅ 策略执行完成，生成 {len(self.portfolio_records)} 个交易记录")

            return True

        except Exception as e:
            print(f"❌ 策略执行失败: {str(e)}")
            return False

    def calculate_performance(self) -> Dict:
        """
        计算性能指标

        Returns:
            Dict: 性能指标
        """
        try:
            if not self.portfolio_records:
                return {}

            portfolio_df = pd.DataFrame(self.portfolio_records)
            returns = portfolio_df['net_return']

            # 计算累计收益
            portfolio_df['cumulative_return'] = (1 + returns).cumprod()

            # 基础指标
            total_return = (portfolio_df['cumulative_return'].iloc[-1] - 1) * 100
            trading_days = len(returns)
            annual_return = ((portfolio_df['cumulative_return'].iloc[-1]) ** (252 / trading_days) - 1) * 100

            # 风险指标
            volatility = returns.std() * np.sqrt(252) * 100
            sharpe_ratio = (annual_return / 100) / (volatility / 100) if volatility > 0 else 0

            # 最大回撤
            peak = portfolio_df['cumulative_return'].expanding().max()
            drawdown = (portfolio_df['cumulative_return'] - peak) / peak
            max_drawdown = drawdown.min() * 100

            # 胜率
            win_rate = (returns > 0).mean() * 100

            # 盈亏比
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]
            avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
            avg_loss = negative_returns.mean() if len(negative_returns) > 0 else 0
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trading_days': trading_days,
                'total_trades': len(self.portfolio_records),
                'avg_daily_return': returns.mean() * 100
            }

            return metrics

        except Exception as e:
            print(f"❌ 计算性能指标失败: {str(e)}")
            return {}

    def create_charts(self) -> bool:
        """
        创建可视化图表

        Returns:
            bool: 图表创建是否成功
        """
        try:
            print("\n📊 创建可视化图表...")

            if not self.portfolio_records:
                print("❌ 没有组合数据")
                return False

            portfolio_df = pd.DataFrame(self.portfolio_records)
            portfolio_df['cumulative_return'] = (1 + portfolio_df['net_return']).cumprod()

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('简化版加密货币量化交易策略回测结果', fontsize=16, fontweight='bold')

            # 1. 累计收益曲线
            ax1 = axes[0, 0]
            ax1.plot(portfolio_df['date'], (portfolio_df['cumulative_return'] - 1) * 100,
                    linewidth=2, color='blue')
            ax1.set_title('累计收益率曲线')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('累计收益率 (%)')
            ax1.grid(True, alpha=0.3)

            # 2. 回撤曲线
            ax2 = axes[0, 1]
            peak = portfolio_df['cumulative_return'].expanding().max()
            drawdown = (portfolio_df['cumulative_return'] - peak) / peak * 100
            ax2.fill_between(portfolio_df['date'], drawdown, 0, color='red', alpha=0.3)
            ax2.set_title('回撤曲线')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('回撤 (%)')
            ax2.grid(True, alpha=0.3)

            # 3. 日收益率分布
            ax3 = axes[1, 0]
            daily_returns = portfolio_df['net_return'] * 100
            ax3.hist(daily_returns, bins=50, alpha=0.7, color='green')
            ax3.axvline(daily_returns.mean(), color='red', linestyle='--',
                       label=f'均值: {daily_returns.mean():.3f}%')
            ax3.set_title('日收益率分布')
            ax3.set_xlabel('日收益率 (%)')
            ax3.set_ylabel('频次')
            ax3.grid(True, alpha=0.3)
            ax3.legend()

            # 4. 滚动夏普比率
            ax4 = axes[1, 1]
            window = 30
            rolling_returns = portfolio_df['net_return'].rolling(window)
            rolling_sharpe = (rolling_returns.mean() / rolling_returns.std()) * np.sqrt(252)
            ax4.plot(portfolio_df['date'][window-1:], rolling_sharpe[window-1:],
                    linewidth=2, color='purple')
            ax4.set_title('30日滚动夏普比率')
            ax4.set_xlabel('日期')
            ax4.set_ylabel('夏普比率')
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            chart_file = os.path.join(self.output_dir, "charts", "backtest_results.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 图表已保存: {chart_file}")

            return True

        except Exception as e:
            print(f"❌ 创建图表失败: {str(e)}")
            return False

    def run_backtest(self) -> bool:
        """
        运行完整回测

        Returns:
            bool: 回测是否成功
        """
        try:
            print("🚀 开始运行简化版加密货币量化交易回测...")
            print("=" * 60)

            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 创建特征
            if not self.create_features():
                return False

            # 3. 滚动预测
            if not self.rolling_prediction():
                return False

            # 4. 执行策略
            if not self.execute_strategy():
                return False

            # 5. 计算性能
            metrics = self.calculate_performance()

            if not metrics:
                print("❌ 无法计算性能指标")
                return False

            # 6. 创建图表
            self.create_charts()

            # 7. 保存报告
            report = {
                'strategy_info': {
                    'name': '简化版加密货币量化交易策略',
                    'start_date': str(self.start_date.date()),
                    'end_date': str(self.end_date.date()),
                    'long_positions': self.long_positions,
                    'short_positions': self.short_positions
                },
                'performance_metrics': metrics,
                'generation_time': datetime.now().isoformat()
            }

            report_file = os.path.join(self.output_dir, "reports", "backtest_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            # 打印结果
            print(f"\n🎉 回测完成！")
            print(f"📊 关键指标:")
            print(f"  💰 总收益率: {metrics['total_return']:.2f}%")
            print(f"  📈 年化收益率: {metrics['annual_return']:.2f}%")
            print(f"  📉 最大回撤: {metrics['max_drawdown']:.2f}%")
            print(f"  ⚡ 夏普比率: {metrics['sharpe_ratio']:.3f}")
            print(f"  🎯 胜率: {metrics['win_rate']:.1f}%")
            print(f"  📊 交易天数: {metrics['trading_days']}")
            print(f"📁 详细结果保存在: {self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 回测失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 启动简化版加密货币量化交易回测系统...")
    print("=" * 60)

    try:
        # 创建回测系统
        backtest = SimplifiedCryptoBacktest()

        # 运行回测
        success = backtest.run_backtest()

        if success:
            print("\n✅ 简化版回测系统运行成功！")
        else:
            print("\n❌ 回测系统运行失败")

        return success

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print("\n程序执行完成")
    sys.exit(0 if success else 1)
