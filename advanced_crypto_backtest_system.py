#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级加密货币量化交易回测系统

基于507个USDT永续合约日K线实体涨跌幅数据，使用LightGBM模型和Qlib Alpha360因子，
构建完整的量化交易回测系统。

核心功能：
- 集成Qlib Alpha360因子集（360个技术指标）
- LightGBM滚动训练预测
- T+2日预测策略（防未来函数）
- 多空选币策略（前5做多，后5做空）
- 完整的回测框架和性能分析

作者：加密货币量化交易系统
日期：2025年1月29日
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import os
import json
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
import qlib
from qlib.data import D
from qlib.config import REG_CN
from qlib.contrib.data.handler import Alpha360

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedCryptoBacktestSystem:
    """高级加密货币量化交易回测系统"""
    
    def __init__(self, 
                 data_dir: str = "daily_body_changes",
                 output_dir: str = "advanced_backtest_results",
                 start_date: str = "2021-01-01",
                 end_date: str = "2025-07-29"):
        """
        初始化回测系统
        
        Args:
            data_dir: 日K线实体涨跌幅数据目录
            output_dir: 回测结果输出目录
            start_date: 回测开始日期
            end_date: 回测结束日期
        """
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        
        # 创建输出目录
        self.create_output_directories()
        
        # 策略参数
        self.long_positions = 5  # 做多币种数量
        self.short_positions = 5  # 做空币种数量
        self.prediction_horizon = 2  # T+2预测
        self.rebalance_freq = 1  # 每日调仓
        
        # 交易成本参数
        self.transaction_cost = 0.001  # 0.1%交易成本
        self.slippage = 0.0005  # 0.05%滑点
        
        # 数据存储
        self.price_data = {}
        self.feature_data = {}
        self.prediction_data = {}
        self.portfolio_data = []
        
        # 模型参数
        self.lgb_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }
        
        print("🚀 高级加密货币量化交易回测系统初始化完成")
        print("=" * 60)
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📅 回测期间: {start_date} ~ {end_date}")
        print(f"🎯 策略配置: 做多{self.long_positions}个，做空{self.short_positions}个")
        print(f"⏰ T+{self.prediction_horizon}预测策略")
    
    def create_output_directories(self):
        """创建输出目录结构"""
        directories = [
            self.output_dir,
            os.path.join(self.output_dir, "models"),
            os.path.join(self.output_dir, "predictions"),
            os.path.join(self.output_dir, "portfolios"),
            os.path.join(self.output_dir, "reports"),
            os.path.join(self.output_dir, "charts")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def load_price_data(self) -> bool:
        """
        加载507个合约的价格数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            print("\n📊 加载507个合约价格数据...")
            
            combined_file = os.path.join(self.data_dir, "combined_data", "all_contracts_body_changes.csv")
            
            if not os.path.exists(combined_file):
                print(f"❌ 合并数据文件不存在: {combined_file}")
                return False
            
            # 读取合并数据
            df = pd.read_csv(combined_file)
            df['date'] = pd.to_datetime(df['date'])
            
            # 筛选时间范围
            df = df[(df['date'] >= self.start_date) & (df['date'] <= self.end_date)]
            
            if df.empty:
                print(f"❌ 指定时间范围内无数据")
                return False
            
            # 按币种分组存储
            symbols = df['symbol'].unique()
            
            for symbol in symbols:
                symbol_data = df[df['symbol'] == symbol].copy()
                symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
                
                # 确保数据连续性
                if len(symbol_data) >= 30:  # 至少30天数据
                    self.price_data[symbol] = symbol_data
            
            print(f"✅ 成功加载 {len(self.price_data)} 个合约数据")
            print(f"📅 数据时间范围: {df['date'].min()} ~ {df['date'].max()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载价格数据失败: {str(e)}")
            return False
    
    def calculate_alpha360_features(self, symbol_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算Alpha360因子特征
        
        Args:
            symbol_data: 单个币种的价格数据
            
        Returns:
            pd.DataFrame: 包含Alpha360特征的数据
        """
        try:
            # 基础价格特征
            df = symbol_data.copy()
            
            # 确保必要的列存在
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in required_cols:
                if col not in df.columns:
                    print(f"⚠️ 缺少必要列: {col}")
                    return df
            
            # 计算技术指标特征（简化版Alpha360）
            
            # 1. 价格相关特征
            for period in [5, 10, 20, 30, 60]:
                # 移动平均
                df[f'ma_{period}'] = df['close'].rolling(period).mean()
                df[f'ma_ratio_{period}'] = df['close'] / df[f'ma_{period}'] - 1
                
                # 价格位置
                df[f'price_position_{period}'] = (df['close'] - df['low'].rolling(period).min()) / \
                                                (df['high'].rolling(period).max() - df['low'].rolling(period).min())
                
                # 收益率
                df[f'return_{period}'] = df['close'].pct_change(period)
                
                # 波动率
                df[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std()
            
            # 2. 成交量特征
            for period in [5, 10, 20]:
                df[f'volume_ma_{period}'] = df['volume'].rolling(period).mean()
                df[f'volume_ratio_{period}'] = df['volume'] / df[f'volume_ma_{period}']
                
                # 量价关系
                df[f'volume_price_corr_{period}'] = df['volume'].rolling(period).corr(df['close'])
            
            # 3. 技术指标
            # RSI
            for period in [14, 30]:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
                rs = gain / loss
                df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            
            # MACD
            ema12 = df['close'].ewm(span=12).mean()
            ema26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema12 - ema26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # 布林带
            for period in [20, 30]:
                ma = df['close'].rolling(period).mean()
                std = df['close'].rolling(period).std()
                df[f'bb_upper_{period}'] = ma + 2 * std
                df[f'bb_lower_{period}'] = ma - 2 * std
                df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / \
                                             (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}'])
            
            # 4. 实体涨跌幅相关特征
            for period in [3, 5, 10, 20]:
                df[f'body_change_ma_{period}'] = df['body_change_pct'].rolling(period).mean()
                df[f'body_change_std_{period}'] = df['body_change_pct'].rolling(period).std()
                df[f'body_size_ma_{period}'] = df['body_size_pct'].rolling(period).mean()
            
            # 5. 趋势特征
            for period in [5, 10, 20]:
                # 趋势强度
                df[f'trend_strength_{period}'] = df['close'].rolling(period).apply(
                    lambda x: np.corrcoef(np.arange(len(x)), x)[0, 1] if len(x) == period else np.nan
                )
                
                # 突破特征
                df[f'breakout_up_{period}'] = (df['close'] > df['high'].rolling(period).max().shift(1)).astype(int)
                df[f'breakout_down_{period}'] = (df['close'] < df['low'].rolling(period).min().shift(1)).astype(int)
            
            # 删除无穷大和NaN值
            df = df.replace([np.inf, -np.inf], np.nan)
            
            return df
            
        except Exception as e:
            print(f"❌ 计算Alpha360特征失败: {str(e)}")
            return symbol_data
    
    def prepare_features_and_targets(self) -> bool:
        """
        准备特征和目标变量
        
        Returns:
            bool: 准备是否成功
        """
        try:
            print("\n🔧 准备特征和目标变量...")
            
            all_features = []
            
            for symbol, data in self.price_data.items():
                print(f"  处理 {symbol}...")
                
                # 计算Alpha360特征
                feature_data = self.calculate_alpha360_features(data)
                
                # 创建目标变量（T+2日实体涨跌幅）
                feature_data['target'] = feature_data['body_change_pct'].shift(-self.prediction_horizon)
                
                # 添加币种标识
                feature_data['symbol'] = symbol
                
                # 只保留有效数据
                feature_data = feature_data.dropna(subset=['target'])
                
                if len(feature_data) > 0:
                    all_features.append(feature_data)
            
            if not all_features:
                print("❌ 没有有效的特征数据")
                return False
            
            # 合并所有特征数据
            self.feature_data = pd.concat(all_features, ignore_index=True)
            
            # 获取特征列（排除非特征列）
            exclude_cols = ['timestamp', 'date', 'symbol', 'target', 'candle_type']
            self.feature_columns = [col for col in self.feature_data.columns if col not in exclude_cols]
            
            print(f"✅ 特征准备完成")
            print(f"📊 总样本数: {len(self.feature_data):,}")
            print(f"🔢 特征数量: {len(self.feature_columns)}")
            print(f"📅 时间范围: {self.feature_data['date'].min()} ~ {self.feature_data['date'].max()}")
            
            return True

        except Exception as e:
            print(f"❌ 准备特征失败: {str(e)}")
            return False

    def rolling_prediction(self) -> bool:
        """
        滚动训练和预测

        Returns:
            bool: 预测是否成功
        """
        try:
            print("\n🤖 开始滚动训练和预测...")

            # 获取所有交易日期
            all_dates = sorted(self.feature_data['date'].unique())

            # 设置最小训练窗口（至少需要60天数据）
            min_train_days = 60

            predictions = []

            for i, current_date in enumerate(all_dates[min_train_days:], min_train_days):
                print(f"  [{i-min_train_days+1}/{len(all_dates)-min_train_days}] 处理日期: {current_date}")

                # 准备训练数据（使用当前日期之前的所有数据）
                train_data = self.feature_data[self.feature_data['date'] < current_date].copy()

                if len(train_data) < min_train_days:
                    continue

                # 准备预测数据（当前日期的数据）
                pred_data = self.feature_data[self.feature_data['date'] == current_date].copy()

                if len(pred_data) == 0:
                    continue

                # 特征和目标变量
                X_train = train_data[self.feature_columns].fillna(0)
                y_train = train_data['target'].fillna(0)
                X_pred = pred_data[self.feature_columns].fillna(0)

                # 训练LightGBM模型
                train_dataset = lgb.Dataset(X_train, label=y_train)

                model = lgb.train(
                    self.lgb_params,
                    train_dataset,
                    num_boost_round=100,
                    valid_sets=[train_dataset],
                    callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
                )

                # 进行预测
                y_pred = model.predict(X_pred)

                # 保存预测结果
                pred_result = pred_data[['date', 'symbol']].copy()
                pred_result['predicted_return'] = y_pred
                pred_result['actual_return'] = pred_data['target'].values

                predictions.append(pred_result)

                # 保存模型（可选）
                if i % 30 == 0:  # 每30天保存一次模型
                    model_file = os.path.join(self.output_dir, "models", f"model_{current_date.strftime('%Y%m%d')}.txt")
                    model.save_model(model_file)

            if not predictions:
                print("❌ 没有生成任何预测")
                return False

            # 合并所有预测结果
            self.prediction_data = pd.concat(predictions, ignore_index=True)

            # 保存预测结果
            pred_file = os.path.join(self.output_dir, "predictions", "all_predictions.csv")
            self.prediction_data.to_csv(pred_file, index=False, encoding='utf-8-sig')

            print(f"✅ 滚动预测完成")
            print(f"📊 预测样本数: {len(self.prediction_data):,}")
            print(f"📅 预测时间范围: {self.prediction_data['date'].min()} ~ {self.prediction_data['date'].max()}")

            return True

        except Exception as e:
            print(f"❌ 滚动预测失败: {str(e)}")
            return False

    def execute_trading_strategy(self) -> bool:
        """
        执行交易策略

        Returns:
            bool: 策略执行是否成功
        """
        try:
            print("\n💼 执行交易策略...")

            # 获取所有交易日期
            trading_dates = sorted(self.prediction_data['date'].unique())

            portfolio_records = []
            current_positions = {}  # 当前持仓

            for trade_date in trading_dates:
                print(f"  处理交易日: {trade_date}")

                # 获取当日预测数据
                daily_predictions = self.prediction_data[
                    self.prediction_data['date'] == trade_date
                ].copy()

                if len(daily_predictions) == 0:
                    continue

                # 排序选币
                daily_predictions = daily_predictions.sort_values('predicted_return', ascending=False)

                # 选择做多和做空的币种
                long_symbols = daily_predictions.head(self.long_positions)['symbol'].tolist()
                short_symbols = daily_predictions.tail(self.short_positions)['symbol'].tolist()

                # 计算目标持仓
                target_positions = {}

                # 做多持仓（每个币种权重相等）
                long_weight = 1.0 / self.long_positions if self.long_positions > 0 else 0
                for symbol in long_symbols:
                    target_positions[symbol] = long_weight

                # 做空持仓（每个币种权重相等，负值表示做空）
                short_weight = -1.0 / self.short_positions if self.short_positions > 0 else 0
                for symbol in short_symbols:
                    target_positions[symbol] = short_weight

                # 计算交易成本和收益
                daily_return = 0.0
                transaction_costs = 0.0

                # 计算持仓收益
                for symbol, weight in current_positions.items():
                    if symbol in daily_predictions['symbol'].values:
                        actual_return = daily_predictions[
                            daily_predictions['symbol'] == symbol
                        ]['actual_return'].iloc[0]

                        if not pd.isna(actual_return):
                            daily_return += weight * actual_return / 100  # 转换为小数

                # 计算调仓成本
                for symbol in set(list(current_positions.keys()) + list(target_positions.keys())):
                    old_weight = current_positions.get(symbol, 0)
                    new_weight = target_positions.get(symbol, 0)

                    if abs(new_weight - old_weight) > 1e-6:
                        transaction_costs += abs(new_weight - old_weight) * self.transaction_cost

                # 更新持仓
                current_positions = target_positions.copy()

                # 记录组合表现
                portfolio_record = {
                    'date': trade_date,
                    'gross_return': daily_return,
                    'transaction_cost': transaction_costs,
                    'net_return': daily_return - transaction_costs,
                    'long_symbols': ','.join(long_symbols),
                    'short_symbols': ','.join(short_symbols),
                    'num_positions': len(current_positions)
                }

                portfolio_records.append(portfolio_record)

            if not portfolio_records:
                print("❌ 没有生成任何交易记录")
                return False

            # 转换为DataFrame
            self.portfolio_data = pd.DataFrame(portfolio_records)

            # 计算累计收益
            self.portfolio_data['cumulative_return'] = (1 + self.portfolio_data['net_return']).cumprod()

            # 保存组合数据
            portfolio_file = os.path.join(self.output_dir, "portfolios", "portfolio_performance.csv")
            self.portfolio_data.to_csv(portfolio_file, index=False, encoding='utf-8-sig')

            print(f"✅ 交易策略执行完成")
            print(f"📊 交易天数: {len(self.portfolio_data)}")
            print(f"📈 总收益率: {(self.portfolio_data['cumulative_return'].iloc[-1] - 1) * 100:.2f}%")

            return True

        except Exception as e:
            print(f"❌ 交易策略执行失败: {str(e)}")
            return False

    def calculate_performance_metrics(self) -> Dict:
        """
        计算性能指标

        Returns:
            Dict: 性能指标字典
        """
        try:
            if self.portfolio_data.empty:
                return {}

            returns = self.portfolio_data['net_return']
            cumulative_returns = self.portfolio_data['cumulative_return']

            # 基础指标
            total_return = (cumulative_returns.iloc[-1] - 1) * 100
            trading_days = len(returns)
            annual_return = ((cumulative_returns.iloc[-1]) ** (252 / trading_days) - 1) * 100

            # 风险指标
            volatility = returns.std() * np.sqrt(252) * 100
            sharpe_ratio = (annual_return / 100) / (volatility / 100) if volatility > 0 else 0

            # 最大回撤
            peak = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - peak) / peak
            max_drawdown = drawdown.min() * 100

            # 胜率
            win_rate = (returns > 0).mean() * 100

            # 盈亏比
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]

            avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
            avg_loss = negative_returns.mean() if len(negative_returns) > 0 else 0
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            # Sortino比率
            downside_returns = returns[returns < 0]
            downside_volatility = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
            sortino_ratio = (annual_return / 100) / (downside_volatility) if downside_volatility > 0 else 0

            # Calmar比率
            calmar_ratio = (annual_return / 100) / abs(max_drawdown / 100) if max_drawdown != 0 else 0

            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trading_days': trading_days,
                'avg_daily_return': returns.mean() * 100,
                'total_transaction_cost': self.portfolio_data['transaction_cost'].sum() * 100
            }

            return metrics

        except Exception as e:
            print(f"❌ 计算性能指标失败: {str(e)}")
            return {}

    def generate_performance_report(self) -> bool:
        """
        生成性能报告

        Returns:
            bool: 报告生成是否成功
        """
        try:
            print("\n📋 生成性能报告...")

            # 计算性能指标
            metrics = self.calculate_performance_metrics()

            if not metrics:
                print("❌ 无法计算性能指标")
                return False

            # 创建详细报告
            report = {
                'strategy_info': {
                    'strategy_name': '高级加密货币量化交易策略',
                    'start_date': str(self.start_date.date()),
                    'end_date': str(self.end_date.date()),
                    'long_positions': self.long_positions,
                    'short_positions': self.short_positions,
                    'prediction_horizon': self.prediction_horizon,
                    'transaction_cost': self.transaction_cost,
                    'slippage': self.slippage
                },
                'performance_metrics': metrics,
                'model_info': {
                    'model_type': 'LightGBM',
                    'feature_count': len(self.feature_columns),
                    'prediction_samples': len(self.prediction_data),
                    'training_method': 'Rolling Window'
                },
                'risk_analysis': {
                    'max_drawdown_date': str(self.portfolio_data.loc[
                        (self.portfolio_data['cumulative_return'] -
                         self.portfolio_data['cumulative_return'].expanding().max()).idxmin(), 'date'
                    ]),
                    'best_day_return': self.portfolio_data['net_return'].max() * 100,
                    'worst_day_return': self.portfolio_data['net_return'].min() * 100,
                    'consecutive_losses': self.calculate_consecutive_losses()
                },
                'generation_time': datetime.now().isoformat()
            }

            # 保存报告
            report_file = os.path.join(self.output_dir, "reports", "performance_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            # 打印关键指标
            print(f"✅ 性能报告生成完成")
            print(f"📊 关键指标:")
            print(f"  💰 总收益率: {metrics['total_return']:.2f}%")
            print(f"  📈 年化收益率: {metrics['annual_return']:.2f}%")
            print(f"  📉 最大回撤: {metrics['max_drawdown']:.2f}%")
            print(f"  ⚡ 夏普比率: {metrics['sharpe_ratio']:.3f}")
            print(f"  🎯 胜率: {metrics['win_rate']:.1f}%")
            print(f"  💸 交易成本: {metrics['total_transaction_cost']:.2f}%")

            return True

        except Exception as e:
            print(f"❌ 生成性能报告失败: {str(e)}")
            return False

    def calculate_consecutive_losses(self) -> int:
        """计算最大连续亏损天数"""
        try:
            returns = self.portfolio_data['net_return']
            max_consecutive = 0
            current_consecutive = 0

            for ret in returns:
                if ret < 0:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0

            return max_consecutive

        except:
            return 0

    def create_visualizations(self) -> bool:
        """
        创建可视化图表

        Returns:
            bool: 可视化是否成功
        """
        try:
            print("\n📊 创建可视化图表...")

            if self.portfolio_data.empty:
                print("❌ 没有组合数据用于可视化")
                return False

            # 设置图表样式
            plt.style.use('seaborn-v0_8')
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('高级加密货币量化交易策略回测结果', fontsize=16, fontweight='bold')

            # 1. 累计收益曲线
            ax1 = axes[0, 0]
            ax1.plot(self.portfolio_data['date'],
                    (self.portfolio_data['cumulative_return'] - 1) * 100,
                    linewidth=2, color='blue', label='策略收益')
            ax1.set_title('累计收益率曲线', fontweight='bold')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('累计收益率 (%)')
            ax1.grid(True, alpha=0.3)
            ax1.legend()

            # 2. 回撤曲线
            ax2 = axes[0, 1]
            peak = self.portfolio_data['cumulative_return'].expanding().max()
            drawdown = (self.portfolio_data['cumulative_return'] - peak) / peak * 100
            ax2.fill_between(self.portfolio_data['date'], drawdown, 0,
                           color='red', alpha=0.3, label='回撤')
            ax2.plot(self.portfolio_data['date'], drawdown,
                    color='red', linewidth=1)
            ax2.set_title('回撤曲线', fontweight='bold')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('回撤 (%)')
            ax2.grid(True, alpha=0.3)
            ax2.legend()

            # 3. 日收益率分布
            ax3 = axes[1, 0]
            daily_returns = self.portfolio_data['net_return'] * 100
            ax3.hist(daily_returns, bins=50, alpha=0.7, color='green', edgecolor='black')
            ax3.axvline(daily_returns.mean(), color='red', linestyle='--',
                       label=f'均值: {daily_returns.mean():.3f}%')
            ax3.set_title('日收益率分布', fontweight='bold')
            ax3.set_xlabel('日收益率 (%)')
            ax3.set_ylabel('频次')
            ax3.grid(True, alpha=0.3)
            ax3.legend()

            # 4. 滚动夏普比率
            ax4 = axes[1, 1]
            window = 30  # 30天滚动窗口
            rolling_returns = self.portfolio_data['net_return'].rolling(window)
            rolling_sharpe = (rolling_returns.mean() / rolling_returns.std()) * np.sqrt(252)

            ax4.plot(self.portfolio_data['date'][window-1:],
                    rolling_sharpe[window-1:],
                    linewidth=2, color='purple', label=f'{window}日滚动夏普比率')
            ax4.axhline(0, color='black', linestyle='-', alpha=0.3)
            ax4.set_title('滚动夏普比率', fontweight='bold')
            ax4.set_xlabel('日期')
            ax4.set_ylabel('夏普比率')
            ax4.grid(True, alpha=0.3)
            ax4.legend()

            plt.tight_layout()

            # 保存图表
            chart_file = os.path.join(self.output_dir, "charts", "strategy_performance.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            # 创建预测准确性分析图
            self.create_prediction_analysis()

            print(f"✅ 可视化图表创建完成")
            print(f"📁 图表保存位置: {self.output_dir}/charts/")

            return True

        except Exception as e:
            print(f"❌ 创建可视化失败: {str(e)}")
            return False

    def create_prediction_analysis(self):
        """创建预测准确性分析图"""
        try:
            if self.prediction_data.empty:
                return

            # 过滤有效预测数据
            valid_predictions = self.prediction_data.dropna(subset=['predicted_return', 'actual_return'])

            if len(valid_predictions) == 0:
                return

            fig, axes = plt.subplots(1, 2, figsize=(15, 6))
            fig.suptitle('预测准确性分析', fontsize=16, fontweight='bold')

            # 1. 预测vs实际散点图
            ax1 = axes[0]
            ax1.scatter(valid_predictions['predicted_return'],
                       valid_predictions['actual_return'],
                       alpha=0.5, s=20)

            # 添加对角线
            min_val = min(valid_predictions['predicted_return'].min(),
                         valid_predictions['actual_return'].min())
            max_val = max(valid_predictions['predicted_return'].max(),
                         valid_predictions['actual_return'].max())
            ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)

            ax1.set_xlabel('预测收益率 (%)')
            ax1.set_ylabel('实际收益率 (%)')
            ax1.set_title('预测 vs 实际收益率')
            ax1.grid(True, alpha=0.3)

            # 计算相关系数
            correlation = valid_predictions['predicted_return'].corr(valid_predictions['actual_return'])
            ax1.text(0.05, 0.95, f'相关系数: {correlation:.3f}',
                    transform=ax1.transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))

            # 2. 预测误差分布
            ax2 = axes[1]
            prediction_error = valid_predictions['actual_return'] - valid_predictions['predicted_return']
            ax2.hist(prediction_error, bins=50, alpha=0.7, color='orange', edgecolor='black')
            ax2.axvline(prediction_error.mean(), color='red', linestyle='--',
                       label=f'均值: {prediction_error.mean():.3f}%')
            ax2.set_xlabel('预测误差 (%)')
            ax2.set_ylabel('频次')
            ax2.set_title('预测误差分布')
            ax2.grid(True, alpha=0.3)
            ax2.legend()

            plt.tight_layout()

            # 保存图表
            pred_chart_file = os.path.join(self.output_dir, "charts", "prediction_analysis.png")
            plt.savefig(pred_chart_file, dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"⚠️ 创建预测分析图失败: {str(e)}")

    def run_complete_backtest(self) -> bool:
        """
        运行完整的回测流程

        Returns:
            bool: 回测是否成功
        """
        try:
            print("🚀 开始运行高级加密货币量化交易回测系统...")
            print("=" * 60)

            # 1. 加载数据
            if not self.load_price_data():
                return False

            # 2. 准备特征和目标变量
            if not self.prepare_features_and_targets():
                return False

            # 3. 滚动预测
            if not self.rolling_prediction():
                return False

            # 4. 执行交易策略
            if not self.execute_trading_strategy():
                return False

            # 5. 生成性能报告
            if not self.generate_performance_report():
                return False

            # 6. 创建可视化
            if not self.create_visualizations():
                return False

            print("\n🎉 高级加密货币量化交易回测完成！")
            print(f"📁 结果保存在: {self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 回测运行失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 启动高级加密货币量化交易回测系统...")
    print("=" * 60)
    print("📊 系统特性:")
    print("  • 基于507个USDT永续合约数据")
    print("  • 集成Alpha360技术指标特征")
    print("  • LightGBM滚动训练预测")
    print("  • T+2日预测策略（防未来函数）")
    print("  • 多空选币策略（前5做多，后5做空）")
    print("  • 完整的性能分析和可视化")
    print("=" * 60)

    # 创建回测系统
    backtest_system = AdvancedCryptoBacktestSystem(
        start_date="2022-01-01",  # 调整开始日期以确保有足够数据
        end_date="2025-07-29"
    )

    try:
        # 运行完整回测
        success = backtest_system.run_complete_backtest()

        if success:
            print("\n✅ 高级加密货币量化交易回测系统运行成功！")
            print(f"📁 详细结果请查看: {backtest_system.output_dir}")
        else:
            print("\n❌ 回测系统运行失败，请检查日志")

        return success

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print("\n按回车键退出...")
    input()
