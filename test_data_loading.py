#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据加载和基本统计

验证507个合约日K线实体涨跌幅数据的加载和基本统计
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime

def test_data_loading():
    """测试数据加载"""
    print("🔍 测试数据加载...")
    print("=" * 50)
    
    try:
        # 数据文件路径
        data_file = "daily_body_changes/combined_data/all_contracts_body_changes.csv"
        
        if not os.path.exists(data_file):
            print(f"❌ 数据文件不存在: {data_file}")
            return False
        
        print(f"📁 数据文件: {data_file}")
        print(f"📊 文件大小: {os.path.getsize(data_file) / 1024 / 1024:.2f} MB")
        
        # 读取数据
        print("\n📖 开始读取数据...")
        data = pd.read_csv(data_file)
        
        print(f"✅ 数据读取成功")
        print(f"📊 数据形状: {data.shape}")
        print(f"🪙 币种数量: {data['symbol'].nunique()}")
        
        # 转换日期
        data['date'] = pd.to_datetime(data['date'])
        print(f"📅 时间范围: {data['date'].min()} ~ {data['date'].max()}")
        
        # 基本统计
        print(f"\n📈 实体涨跌幅统计:")
        print(f"  平均值: {data['body_change_pct'].mean():.4f}%")
        print(f"  标准差: {data['body_change_pct'].std():.4f}%")
        print(f"  最小值: {data['body_change_pct'].min():.4f}%")
        print(f"  最大值: {data['body_change_pct'].max():.4f}%")
        
        # 按币种统计
        print(f"\n🪙 各币种数据量统计:")
        symbol_counts = data['symbol'].value_counts()
        print(f"  数据量最多: {symbol_counts.index[0]} ({symbol_counts.iloc[0]} 条)")
        print(f"  数据量最少: {symbol_counts.index[-1]} ({symbol_counts.iloc[-1]} 条)")
        print(f"  平均数据量: {symbol_counts.mean():.0f} 条")
        
        # 时间范围筛选测试
        start_date = pd.to_datetime("2022-01-01")
        end_date = pd.to_datetime("2025-07-29")
        
        filtered_data = data[(data['date'] >= start_date) & (data['date'] <= end_date)]
        print(f"\n📅 筛选后数据 ({start_date.date()} ~ {end_date.date()}):")
        print(f"  数据量: {len(filtered_data):,} 条")
        print(f"  币种数: {filtered_data['symbol'].nunique()}")
        
        # 检查缺失值
        print(f"\n🔍 缺失值检查:")
        missing_counts = data.isnull().sum()
        for col, count in missing_counts.items():
            if count > 0:
                print(f"  {col}: {count} 个缺失值")
        
        if missing_counts.sum() == 0:
            print("  ✅ 无缺失值")
        
        # 检查数据类型
        print(f"\n📋 数据类型:")
        for col, dtype in data.dtypes.items():
            print(f"  {col}: {dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {str(e)}")
        return False

def test_feature_creation():
    """测试特征创建"""
    print(f"\n🔧 测试特征创建...")
    print("=" * 50)
    
    try:
        # 读取数据
        data_file = "daily_body_changes/combined_data/all_contracts_body_changes.csv"
        data = pd.read_csv(data_file)
        data['date'] = pd.to_datetime(data['date'])
        
        # 选择一个币种进行测试
        test_symbol = data['symbol'].value_counts().index[0]  # 选择数据最多的币种
        symbol_data = data[data['symbol'] == test_symbol].copy()
        symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
        
        print(f"🪙 测试币种: {test_symbol}")
        print(f"📊 数据量: {len(symbol_data)} 条")
        
        # 创建简单特征
        print(f"\n🔧 创建技术指标特征...")
        
        # 移动平均
        for period in [5, 10, 20]:
            symbol_data[f'body_ma_{period}'] = symbol_data['body_change_pct'].rolling(period).mean()
            symbol_data[f'close_ma_{period}'] = symbol_data['close'].rolling(period).mean()
            symbol_data[f'return_{period}'] = symbol_data['close'].pct_change(period)
        
        # RSI
        delta = symbol_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        symbol_data['rsi_14'] = 100 - (100 / (1 + rs))
        
        # 目标变量
        symbol_data['target'] = symbol_data['body_change_pct'].shift(-2)  # T+2预测
        
        # 删除缺失值
        clean_data = symbol_data.dropna()
        
        print(f"✅ 特征创建成功")
        print(f"📊 清理后数据量: {len(clean_data)} 条")
        print(f"🔢 特征数量: {len([col for col in clean_data.columns if col.endswith(('_ma_5', '_ma_10', '_ma_20', 'rsi_14'))])}")
        
        # 显示特征统计
        feature_cols = [col for col in clean_data.columns if col.endswith(('_ma_5', '_ma_10', '_ma_20', 'rsi_14'))]
        print(f"\n📈 特征统计:")
        for col in feature_cols[:5]:  # 只显示前5个特征
            print(f"  {col}: 均值={clean_data[col].mean():.4f}, 标准差={clean_data[col].std():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征创建失败: {str(e)}")
        return False

def test_simple_prediction():
    """测试简单预测"""
    print(f"\n🤖 测试简单预测...")
    print("=" * 50)
    
    try:
        import lightgbm as lgb
        
        # 读取数据
        data_file = "daily_body_changes/combined_data/all_contracts_body_changes.csv"
        data = pd.read_csv(data_file)
        data['date'] = pd.to_datetime(data['date'])
        
        # 筛选时间范围（使用较小的数据集进行测试）
        start_date = pd.to_datetime("2024-01-01")
        end_date = pd.to_datetime("2025-07-29")
        data = data[(data['date'] >= start_date) & (data['date'] <= end_date)]
        
        print(f"📊 测试数据量: {len(data):,} 条")
        print(f"🪙 币种数量: {data['symbol'].nunique()}")
        
        # 选择前10个币种进行快速测试
        top_symbols = data['symbol'].value_counts().head(10).index.tolist()
        test_data = data[data['symbol'].isin(top_symbols)].copy()
        
        print(f"🎯 测试币种: {len(top_symbols)} 个")
        print(f"📊 测试数据量: {len(test_data):,} 条")
        
        # 创建简单特征
        feature_data = []
        for symbol in top_symbols:
            symbol_data = test_data[test_data['symbol'] == symbol].copy()
            symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
            
            if len(symbol_data) < 30:
                continue
            
            # 简单特征
            symbol_data['body_ma_5'] = symbol_data['body_change_pct'].rolling(5).mean()
            symbol_data['body_ma_10'] = symbol_data['body_change_pct'].rolling(10).mean()
            symbol_data['return_5'] = symbol_data['close'].pct_change(5)
            symbol_data['target'] = symbol_data['body_change_pct'].shift(-2)
            
            symbol_data = symbol_data.dropna()
            if len(symbol_data) > 0:
                feature_data.append(symbol_data)
        
        if not feature_data:
            print("❌ 没有有效的特征数据")
            return False
        
        # 合并数据
        combined_data = pd.concat(feature_data, ignore_index=True)
        print(f"📊 合并后数据量: {len(combined_data):,} 条")
        
        # 准备训练数据
        feature_cols = ['body_ma_5', 'body_ma_10', 'return_5']
        X = combined_data[feature_cols].fillna(0)
        y = combined_data['target'].fillna(0)
        
        # 简单的训练测试分割
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        print(f"📊 训练集: {len(X_train)} 条")
        print(f"📊 测试集: {len(X_test)} 条")
        
        # 训练模型
        print(f"\n🤖 训练LightGBM模型...")
        train_dataset = lgb.Dataset(X_train, label=y_train)
        
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'verbose': -1
        }
        
        model = lgb.train(params, train_dataset, num_boost_round=50)
        
        # 预测
        y_pred = model.predict(X_test)
        
        # 计算简单指标
        mse = np.mean((y_test - y_pred) ** 2)
        rmse = np.sqrt(mse)
        correlation = np.corrcoef(y_test, y_pred)[0, 1]
        
        print(f"✅ 模型训练完成")
        print(f"📊 预测结果:")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  相关系数: {correlation:.4f}")
        print(f"  预测范围: {y_pred.min():.4f} ~ {y_pred.max():.4f}")
        print(f"  实际范围: {y_test.min():.4f} ~ {y_test.max():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预测测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 开始数据加载和预测测试...")
    print("=" * 60)
    
    try:
        # 测试数据加载
        if not test_data_loading():
            return False
        
        # 测试特征创建
        if not test_feature_creation():
            return False
        
        # 测试简单预测
        if not test_simple_prediction():
            return False
        
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 数据加载正常")
        print(f"✅ 特征创建正常") 
        print(f"✅ 模型预测正常")
        print(f"\n📋 可以继续运行完整的回测系统")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n测试完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
