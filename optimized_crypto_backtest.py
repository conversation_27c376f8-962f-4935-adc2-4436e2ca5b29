#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版加密货币量化交易回测系统

基于507个USDT永续合约日K线实体涨跌幅数据的优化回测系统
- 高效的数据处理和特征工程
- LightGBM滚动训练预测
- T+2日预测策略（防未来函数）
- 多空选币策略（前5做多，后5做空）
- 完整的性能分析和可视化

作者：加密货币量化交易系统
日期：2025年1月29日
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import os
import json
import warnings
from datetime import datetime
import matplotlib.pyplot as plt
import sys

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedCryptoBacktest:
    """优化版加密货币量化交易回测系统"""

    def __init__(self):
        """初始化回测系统"""
        self.data_file = "daily_body_changes/combined_data/all_contracts_body_changes.csv"
        self.output_dir = "optimized_backtest_results"
        self.start_date = "2022-01-01"
        self.end_date = "2025-07-29"

        # 策略参数
        self.long_positions = 5
        self.short_positions = 5
        self.prediction_horizon = 2
        self.transaction_cost = 0.001
        self.min_data_points = 60  # 最少需要的历史数据点

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "reports"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "charts"), exist_ok=True)

        # 数据存储
        self.data = None
        self.predictions = []
        self.portfolio_records = []

        print("🚀 优化版加密货币量化交易回测系统")
        print("=" * 60)
        print(f"📅 回测期间: {self.start_date} ~ {self.end_date}")
        print(f"🎯 策略: 做多{self.long_positions}个，做空{self.short_positions}个")
        print(f"⏰ T+{self.prediction_horizon}预测策略")

    def load_and_prepare_data(self):
        """加载和准备数据"""
        try:
            print(f"\n📊 加载数据...")

            # 读取数据
            self.data = pd.read_csv(self.data_file)
            self.data['date'] = pd.to_datetime(self.data['date'])

            # 筛选时间范围
            start_dt = pd.to_datetime(self.start_date)
            end_dt = pd.to_datetime(self.end_date)
            self.data = self.data[(self.data['date'] >= start_dt) & (self.data['date'] <= end_dt)]

            print(f"✅ 数据加载完成")
            print(f"📊 数据量: {len(self.data):,} 条")
            print(f"🪙 币种数: {self.data['symbol'].nunique()}")
            print(f"📅 时间范围: {self.data['date'].min().date()} ~ {self.data['date'].max().date()}")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False

    def create_features_batch(self):
        """批量创建特征"""
        try:
            print(f"\n🔧 批量创建特征...")

            feature_data = []
            symbols = self.data['symbol'].unique()

            for i, symbol in enumerate(symbols):
                if i % 50 == 0:
                    print(f"  处理进度: {i+1}/{len(symbols)} ({(i+1)/len(symbols)*100:.1f}%)")

                symbol_data = self.data[self.data['symbol'] == symbol].copy()
                symbol_data = symbol_data.sort_values('date').reset_index(drop=True)

                if len(symbol_data) < 30:
                    continue

                # 快速特征计算
                # 移动平均特征
                symbol_data['body_ma_5'] = symbol_data['body_change_pct'].rolling(5, min_periods=1).mean()
                symbol_data['body_ma_10'] = symbol_data['body_change_pct'].rolling(10, min_periods=1).mean()
                symbol_data['body_ma_20'] = symbol_data['body_change_pct'].rolling(20, min_periods=1).mean()

                # 价格特征
                symbol_data['close_ma_5'] = symbol_data['close'].rolling(5, min_periods=1).mean()
                symbol_data['close_ma_10'] = symbol_data['close'].rolling(10, min_periods=1).mean()
                symbol_data['price_ratio_5'] = symbol_data['close'] / symbol_data['close_ma_5'] - 1
                symbol_data['price_ratio_10'] = symbol_data['close'] / symbol_data['close_ma_10'] - 1

                # 波动率特征
                symbol_data['volatility_5'] = symbol_data['body_change_pct'].rolling(5, min_periods=1).std()
                symbol_data['volatility_10'] = symbol_data['body_change_pct'].rolling(10, min_periods=1).std()

                # 趋势特征
                symbol_data['return_5'] = symbol_data['close'].pct_change(5)
                symbol_data['return_10'] = symbol_data['close'].pct_change(10)

                # RSI特征
                delta = symbol_data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(14, min_periods=1).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14, min_periods=1).mean()
                rs = gain / (loss + 1e-8)  # 避免除零
                symbol_data['rsi'] = 100 - (100 / (1 + rs))

                # 目标变量
                symbol_data['target'] = symbol_data['body_change_pct'].shift(-self.prediction_horizon)

                # 删除缺失的目标变量
                symbol_data = symbol_data.dropna(subset=['target'])

                if len(symbol_data) > 0:
                    feature_data.append(symbol_data)

            if not feature_data:
                print("❌ 没有有效的特征数据")
                return False

            # 合并所有数据
            self.data = pd.concat(feature_data, ignore_index=True)
            self.data = self.data.sort_values(['date', 'symbol']).reset_index(drop=True)

            # 定义特征列
            self.feature_columns = [
                'body_ma_5', 'body_ma_10', 'body_ma_20',
                'price_ratio_5', 'price_ratio_10',
                'volatility_5', 'volatility_10',
                'return_5', 'return_10', 'rsi',
                'body_size_pct', 'upper_shadow_pct', 'lower_shadow_pct', 'volatility_pct'
            ]

            print(f"✅ 特征创建完成")
            print(f"📊 有效数据: {len(self.data):,} 条")
            print(f"🔢 特征数量: {len(self.feature_columns)}")

            return True

        except Exception as e:
            print(f"❌ 特征创建失败: {str(e)}")
            return False

    def rolling_prediction_optimized(self):
        """优化的滚动预测"""
        try:
            print(f"\n🤖 开始优化滚动预测...")

            # 获取所有交易日期
            all_dates = sorted(self.data['date'].unique())

            # 设置预测参数
            prediction_start_idx = self.min_data_points
            total_dates = len(all_dates) - prediction_start_idx

            print(f"📊 预测设置:")
            print(f"  总交易日: {len(all_dates)}")
            print(f"  预测开始: 第{prediction_start_idx+1}天")
            print(f"  预测天数: {total_dates}")

            # LightGBM参数
            lgb_params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.8,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }

            for i, current_date in enumerate(all_dates[prediction_start_idx:], prediction_start_idx):
                if i % 30 == 0:
                    progress = (i - prediction_start_idx + 1) / total_dates * 100
                    print(f"  预测进度: {i-prediction_start_idx+1}/{total_dates} ({progress:.1f}%)")

                # 准备训练数据（使用过去60天的数据）
                train_end_date = current_date
                train_start_idx = max(0, i - 60)
                train_dates = all_dates[train_start_idx:i]

                train_data = self.data[self.data['date'].isin(train_dates)]
                pred_data = self.data[self.data['date'] == current_date]

                if len(train_data) < 30 or len(pred_data) == 0:
                    continue

                # 准备特征和目标
                X_train = train_data[self.feature_columns].fillna(0)
                y_train = train_data['target'].fillna(0)
                X_pred = pred_data[self.feature_columns].fillna(0)

                # 训练模型
                train_dataset = lgb.Dataset(X_train, label=y_train)
                model = lgb.train(
                    lgb_params,
                    train_dataset,
                    num_boost_round=50,
                    callbacks=[lgb.log_evaluation(0)]
                )

                # 预测
                y_pred = model.predict(X_pred)

                # 保存预测结果
                for j, (idx, row) in enumerate(pred_data.iterrows()):
                    self.predictions.append({
                        'date': current_date,
                        'symbol': row['symbol'],
                        'predicted_return': y_pred[j],
                        'actual_return': row['target']
                    })

            print(f"✅ 滚动预测完成")
            print(f"📊 预测记录: {len(self.predictions):,} 条")

            return True

        except Exception as e:
            print(f"❌ 滚动预测失败: {str(e)}")
            return False

    def execute_trading_strategy(self):
        """执行交易策略"""
        try:
            print(f"\n💼 执行交易策略...")

            # 转换预测为DataFrame
            pred_df = pd.DataFrame(self.predictions)

            if pred_df.empty:
                print("❌ 没有预测数据")
                return False

            # 按日期分组执行策略
            trading_dates = sorted(pred_df['date'].unique())
            current_positions = {}

            print(f"📊 策略执行设置:")
            print(f"  交易天数: {len(trading_dates)}")
            print(f"  做多数量: {self.long_positions}")
            print(f"  做空数量: {self.short_positions}")
            print(f"  交易成本: {self.transaction_cost*100:.1f}%")

            for i, trade_date in enumerate(trading_dates):
                if i % 100 == 0:
                    print(f"  策略进度: {i+1}/{len(trading_dates)} ({(i+1)/len(trading_dates)*100:.1f}%)")

                daily_pred = pred_df[pred_df['date'] == trade_date].copy()

                if len(daily_pred) < self.long_positions + self.short_positions:
                    continue

                # 按预测收益率排序选币
                daily_pred = daily_pred.sort_values('predicted_return', ascending=False)

                # 选择做多和做空的币种
                long_symbols = daily_pred.head(self.long_positions)['symbol'].tolist()
                short_symbols = daily_pred.tail(self.short_positions)['symbol'].tolist()

                # 计算当日收益
                daily_return = 0.0
                transaction_costs = 0.0

                # 计算持仓收益
                for symbol, weight in current_positions.items():
                    symbol_data = daily_pred[daily_pred['symbol'] == symbol]
                    if not symbol_data.empty and not pd.isna(symbol_data['actual_return'].iloc[0]):
                        actual_return = symbol_data['actual_return'].iloc[0]
                        daily_return += weight * actual_return / 100

                # 更新持仓
                new_positions = {}
                long_weight = 1.0 / self.long_positions if self.long_positions > 0 else 0
                short_weight = -1.0 / self.short_positions if self.short_positions > 0 else 0

                for symbol in long_symbols:
                    new_positions[symbol] = long_weight
                for symbol in short_symbols:
                    new_positions[symbol] = short_weight

                # 计算调仓成本
                all_symbols = set(list(current_positions.keys()) + list(new_positions.keys()))
                for symbol in all_symbols:
                    old_weight = current_positions.get(symbol, 0)
                    new_weight = new_positions.get(symbol, 0)
                    if abs(new_weight - old_weight) > 1e-6:
                        transaction_costs += abs(new_weight - old_weight) * self.transaction_cost

                current_positions = new_positions.copy()

                # 记录组合表现
                self.portfolio_records.append({
                    'date': trade_date,
                    'gross_return': daily_return,
                    'transaction_cost': transaction_costs,
                    'net_return': daily_return - transaction_costs,
                    'long_symbols': ','.join(long_symbols),
                    'short_symbols': ','.join(short_symbols),
                    'num_positions': len(current_positions)
                })

            print(f"✅ 策略执行完成")
            print(f"📊 交易记录: {len(self.portfolio_records)} 条")

            return True

        except Exception as e:
            print(f"❌ 策略执行失败: {str(e)}")
            return False

    def calculate_performance_metrics(self):
        """计算性能指标"""
        try:
            if not self.portfolio_records:
                return {}

            portfolio_df = pd.DataFrame(self.portfolio_records)
            returns = portfolio_df['net_return']

            # 计算累计收益
            portfolio_df['cumulative_return'] = (1 + returns).cumprod()

            # 基础指标
            total_return = (portfolio_df['cumulative_return'].iloc[-1] - 1) * 100
            trading_days = len(returns)
            annual_return = ((portfolio_df['cumulative_return'].iloc[-1]) ** (252 / trading_days) - 1) * 100

            # 风险指标
            volatility = returns.std() * np.sqrt(252) * 100
            sharpe_ratio = (annual_return / 100) / (volatility / 100) if volatility > 0 else 0

            # 最大回撤
            peak = portfolio_df['cumulative_return'].expanding().max()
            drawdown = (portfolio_df['cumulative_return'] - peak) / peak
            max_drawdown = drawdown.min() * 100

            # 胜率和盈亏比
            win_rate = (returns > 0).mean() * 100
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]
            avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
            avg_loss = negative_returns.mean() if len(negative_returns) > 0 else 0
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            # Sortino比率
            downside_returns = returns[returns < 0]
            downside_volatility = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
            sortino_ratio = (annual_return / 100) / (downside_volatility) if downside_volatility > 0 else 0

            # Calmar比率
            calmar_ratio = (annual_return / 100) / abs(max_drawdown / 100) if max_drawdown != 0 else 0

            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trading_days': trading_days,
                'avg_daily_return': returns.mean() * 100,
                'total_transaction_cost': portfolio_df['transaction_cost'].sum() * 100,
                'best_day': returns.max() * 100,
                'worst_day': returns.min() * 100
            }

            return metrics, portfolio_df

        except Exception as e:
            print(f"❌ 计算性能指标失败: {str(e)}")
            return {}, None

    def create_performance_charts(self, portfolio_df):
        """创建性能图表"""
        try:
            print(f"\n📊 创建性能图表...")

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('优化版加密货币量化交易策略回测结果', fontsize=16, fontweight='bold')

            # 1. 累计收益曲线
            ax1 = axes[0, 0]
            ax1.plot(portfolio_df['date'], (portfolio_df['cumulative_return'] - 1) * 100,
                    linewidth=2, color='blue', label='策略收益')
            ax1.set_title('累计收益率曲线', fontweight='bold')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('累计收益率 (%)')
            ax1.grid(True, alpha=0.3)
            ax1.legend()

            # 2. 回撤曲线
            ax2 = axes[0, 1]
            peak = portfolio_df['cumulative_return'].expanding().max()
            drawdown = (portfolio_df['cumulative_return'] - peak) / peak * 100
            ax2.fill_between(portfolio_df['date'], drawdown, 0, color='red', alpha=0.3)
            ax2.plot(portfolio_df['date'], drawdown, color='red', linewidth=1)
            ax2.set_title('回撤曲线', fontweight='bold')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('回撤 (%)')
            ax2.grid(True, alpha=0.3)

            # 3. 日收益率分布
            ax3 = axes[1, 0]
            daily_returns = portfolio_df['net_return'] * 100
            ax3.hist(daily_returns, bins=50, alpha=0.7, color='green', edgecolor='black')
            ax3.axvline(daily_returns.mean(), color='red', linestyle='--',
                       label=f'均值: {daily_returns.mean():.3f}%')
            ax3.set_title('日收益率分布', fontweight='bold')
            ax3.set_xlabel('日收益率 (%)')
            ax3.set_ylabel('频次')
            ax3.grid(True, alpha=0.3)
            ax3.legend()

            # 4. 滚动夏普比率
            ax4 = axes[1, 1]
            window = 30
            rolling_returns = portfolio_df['net_return'].rolling(window)
            rolling_sharpe = (rolling_returns.mean() / rolling_returns.std()) * np.sqrt(252)
            ax4.plot(portfolio_df['date'][window-1:], rolling_sharpe[window-1:],
                    linewidth=2, color='purple', label=f'{window}日滚动夏普比率')
            ax4.axhline(0, color='black', linestyle='-', alpha=0.3)
            ax4.set_title('滚动夏普比率', fontweight='bold')
            ax4.set_xlabel('日期')
            ax4.set_ylabel('夏普比率')
            ax4.grid(True, alpha=0.3)
            ax4.legend()

            plt.tight_layout()

            # 保存图表
            chart_file = os.path.join(self.output_dir, "charts", "performance_charts.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 图表已保存: {chart_file}")

            return True

        except Exception as e:
            print(f"❌ 创建图表失败: {str(e)}")
            return False

    def save_results(self, metrics, portfolio_df):
        """保存结果"""
        try:
            print(f"\n💾 保存结果...")

            # 保存组合数据
            portfolio_file = os.path.join(self.output_dir, "reports", "portfolio_performance.csv")
            portfolio_df.to_csv(portfolio_file, index=False, encoding='utf-8-sig')

            # 保存预测数据
            pred_file = os.path.join(self.output_dir, "reports", "predictions.csv")
            pred_df = pd.DataFrame(self.predictions)
            pred_df.to_csv(pred_file, index=False, encoding='utf-8-sig')

            # 创建详细报告
            report = {
                'strategy_info': {
                    'name': '优化版加密货币量化交易策略',
                    'start_date': self.start_date,
                    'end_date': self.end_date,
                    'long_positions': self.long_positions,
                    'short_positions': self.short_positions,
                    'prediction_horizon': self.prediction_horizon,
                    'transaction_cost': self.transaction_cost,
                    'min_data_points': self.min_data_points
                },
                'performance_metrics': metrics,
                'data_info': {
                    'total_predictions': len(self.predictions),
                    'total_trades': len(self.portfolio_records),
                    'feature_count': len(self.feature_columns),
                    'data_points': len(self.data)
                },
                'generation_time': datetime.now().isoformat()
            }

            # 保存JSON报告
            report_file = os.path.join(self.output_dir, "reports", "backtest_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"✅ 结果保存完成")
            print(f"📁 输出目录: {self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 保存结果失败: {str(e)}")
            return False

    def run_complete_backtest(self):
        """运行完整回测"""
        try:
            print("🚀 开始运行优化版加密货币量化交易回测...")
            print("=" * 60)

            # 1. 加载数据
            if not self.load_and_prepare_data():
                return False

            # 2. 创建特征
            if not self.create_features_batch():
                return False

            # 3. 滚动预测
            if not self.rolling_prediction_optimized():
                return False

            # 4. 执行策略
            if not self.execute_trading_strategy():
                return False

            # 5. 计算性能
            metrics, portfolio_df = self.calculate_performance_metrics()

            if not metrics:
                print("❌ 无法计算性能指标")
                return False

            # 6. 创建图表
            self.create_performance_charts(portfolio_df)

            # 7. 保存结果
            self.save_results(metrics, portfolio_df)

            # 8. 打印结果
            print(f"\n🎉 优化版回测完成！")
            print(f"📊 关键指标:")
            print(f"  💰 总收益率: {metrics['total_return']:.2f}%")
            print(f"  📈 年化收益率: {metrics['annual_return']:.2f}%")
            print(f"  📉 最大回撤: {metrics['max_drawdown']:.2f}%")
            print(f"  ⚡ 夏普比率: {metrics['sharpe_ratio']:.3f}")
            print(f"  🎯 胜率: {metrics['win_rate']:.1f}%")
            print(f"  💸 总交易成本: {metrics['total_transaction_cost']:.2f}%")
            print(f"  📊 交易天数: {metrics['trading_days']}")
            print(f"  🔥 最佳单日: {metrics['best_day']:.2f}%")
            print(f"  ❄️ 最差单日: {metrics['worst_day']:.2f}%")

            return True

        except Exception as e:
            print(f"❌ 回测失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 启动优化版加密货币量化交易回测系统...")
    print("=" * 60)

    try:
        # 创建回测系统
        backtest = OptimizedCryptoBacktest()

        # 运行回测
        success = backtest.run_complete_backtest()

        if success:
            print("\n✅ 优化版回测系统运行成功！")
            print(f"📁 详细结果请查看: {backtest.output_dir}")
        else:
            print("\n❌ 回测系统运行失败")

        return success

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n程序执行完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
"""
优化版加密货币量化交易回测系统
Optimized Cryptocurrency Quantitative Trading Backtest System

优化内容：
1. 改进止损恢复机制
2. 动态调整交易参数
3. 增加市场状态判断
4. 优化风险管理策略

作者：AI Assistant
创建时间：2025-07-13
版本：3.0 (优化版)
"""

import os
import sys
import time
import warnings
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import traceback

# 数据处理和科学计算
import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score

# 机器学习
import lightgbm as lgb

# 可视化
import matplotlib.pyplot as plt
import seaborn as sns

# 忽略警告
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedCryptoBacktest:
    """优化版加密货币量化交易回测系统"""
    
    def __init__(self, 
                 start_date: str = "2020-01-01",
                 end_date: str = "2024-12-31",
                 initial_capital: float = 100000.0,
                 max_positions: int = 3,
                 single_position_limit: float = 0.25,
                 stop_loss_threshold: float = 0.12):
        """
        初始化回测系统
        """
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.max_positions = max_positions
        self.single_position_limit = single_position_limit
        self.stop_loss_threshold = stop_loss_threshold
        
        # 数据存储
        self.price_data = {}
        self.features_data = {}
        self.portfolio_history = []
        self.trade_history = []
        
        # 模型和预处理器
        self.models = {}
        self.scalers = {}
        
        # 性能指标
        self.performance_metrics = {}
        
        # 优化的风险管理状态
        self.stop_loss_triggered = False
        self.stop_loss_trigger_date = None
        self.days_in_stop_loss = 0
        self.max_stop_loss_days = 30  # 最多止损30天
        self.recovery_threshold = 0.08  # 恢复交易的回撤阈值
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3
        
        # 市场状态
        self.market_volatility = 0.0
        self.market_trend = 0  # 1: 上涨, -1: 下跌, 0: 震荡
        
        # 设置日志
        self.setup_logging()
        
        self.logger.info("🚀 优化版加密货币量化交易回测系统初始化完成")
        self.logger.info(f"📅 回测期间: {start_date} 至 {end_date}")
        self.logger.info(f"💰 初始资金: ${initial_capital:,.2f}")
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('optimized_backtest.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_local_data(self) -> bool:
        """加载本地数据文件"""
        self.logger.info("=" * 60)
        self.logger.info("第1阶段：加载本地数据")
        self.logger.info("=" * 60)
        
        try:
            if not os.path.exists('data'):
                self.logger.error("❌ 数据目录不存在")
                return False
                
            # 查找所有原始数据文件
            raw_files = [f for f in os.listdir('data') if f.endswith('_ccxt_raw.csv')]
            
            if not raw_files:
                self.logger.error("❌ 没有找到原始数据文件")
                return False
                
            successful_symbols = []
            
            for file in raw_files:
                try:
                    # 解析币种符号
                    symbol = file.replace('_ccxt_raw.csv', '').replace('_', '/')
                    
                    self.logger.info(f"  正在加载 {symbol} 数据...")
                    
                    # 加载数据
                    df = pd.read_csv(f'data/{file}')
                    
                    # 处理日期列
                    if 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                    else:
                        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                        df.set_index('timestamp', inplace=True)
                        
                    # 选择需要的列
                    df = df[['open', 'high', 'low', 'close', 'volume']]
                    
                    # 过滤日期范围
                    df = df[(df.index >= self.start_date) & (df.index <= self.end_date)]
                    
                    if len(df) > 100:  # 至少需要100天的数据
                        self.price_data[symbol] = df
                        successful_symbols.append(symbol)
                        self.logger.info(f"    ✅ {symbol}: {len(df)} 条记录")
                    else:
                        self.logger.warning(f"    ⚠️ {symbol}: 数据不足 ({len(df)} 条)")
                        
                except Exception as file_e:
                    self.logger.warning(f"    ❌ 加载 {file} 失败: {str(file_e)}")
                    continue
                    
            if successful_symbols:
                self.crypto_symbols = successful_symbols
                self.logger.info(f"✅ 本地数据加载完成，成功加载 {len(successful_symbols)} 个币种数据")
                return True
            else:
                self.logger.error("❌ 没有可用的本地数据")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 本地数据加载失败: {str(e)}")
            return False
            
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        data = df.copy()
        
        # 基础价格指标
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # 移动平均线
        for period in [5, 10, 20]:
            data[f'ma_{period}'] = data['close'].rolling(window=period).mean()
            data[f'ma_ratio_{period}'] = data['close'] / data[f'ma_{period}']
            
        # RSI指标
        def calculate_rsi(prices, period=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
            
        data['rsi_14'] = calculate_rsi(data['close'], 14)
        
        # 布林带
        bb_period = 20
        bb_ma = data['close'].rolling(window=bb_period).mean()
        bb_std = data['close'].rolling(window=bb_period).std()
        data['bb_upper'] = bb_ma + (bb_std * 2)
        data['bb_lower'] = bb_ma - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # 成交量指标
        data['volume_ma'] = data['volume'].rolling(window=10).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        
        # 波动率
        data['volatility'] = data['returns'].rolling(window=10).std()
        
        # 动量指标
        data['momentum_5'] = data['close'] / data['close'].shift(5) - 1
        
        # 市场强度指标
        data['price_strength'] = (data['close'] - data['low']) / (data['high'] - data['low'])
        
        return data

    def create_features(self) -> bool:
        """为所有币种创建特征"""
        self.logger.info("=" * 60)
        self.logger.info("第2阶段：特征工程")
        self.logger.info("=" * 60)

        try:
            for symbol in self.crypto_symbols:
                self.logger.info(f"  正在为 {symbol} 创建特征...")

                # 计算技术指标
                df_with_indicators = self.calculate_technical_indicators(self.price_data[symbol])

                # 创建目标变量（次日收益率）
                df_with_indicators['target_return'] = df_with_indicators['close'].shift(-1) / df_with_indicators['close'] - 1
                df_with_indicators['target_direction'] = (df_with_indicators['target_return'] > 0).astype(int)

                # 移除无穷大和NaN值
                df_with_indicators = df_with_indicators.replace([np.inf, -np.inf], np.nan)
                df_with_indicators = df_with_indicators.dropna()

                self.features_data[symbol] = df_with_indicators
                self.logger.info(f"    ✅ {symbol}: {len(df_with_indicators)} 条记录，{len(df_with_indicators.columns)} 个特征")

            self.logger.info(f"✅ 特征工程完成，处理了 {len(self.features_data)} 个币种")
            return True

        except Exception as e:
            self.logger.error(f"❌ 特征工程失败: {str(e)}")
            return False

    def assess_market_condition(self, current_date: str) -> Dict:
        """评估市场状态"""
        try:
            market_returns = []
            market_volatilities = []

            for symbol in self.crypto_symbols:
                if symbol in self.features_data:
                    df = self.features_data[symbol]
                    recent_data = df[df.index <= current_date].tail(20)  # 最近20天

                    if len(recent_data) >= 10:
                        returns = recent_data['returns'].dropna()
                        if len(returns) > 0:
                            market_returns.extend(returns.tolist())
                            market_volatilities.append(returns.std())

            if market_returns:
                avg_return = np.mean(market_returns)
                avg_volatility = np.mean(market_volatilities)

                # 判断市场趋势
                if avg_return > 0.01:
                    trend = 1  # 上涨
                elif avg_return < -0.01:
                    trend = -1  # 下跌
                else:
                    trend = 0  # 震荡

                self.market_volatility = avg_volatility
                self.market_trend = trend

                return {
                    'trend': trend,
                    'volatility': avg_volatility,
                    'avg_return': avg_return
                }

            return {'trend': 0, 'volatility': 0.02, 'avg_return': 0.0}

        except Exception as e:
            self.logger.warning(f"市场状态评估失败: {str(e)}")
            return {'trend': 0, 'volatility': 0.02, 'avg_return': 0.0}

    def train_model(self, symbol: str, train_end_date: str) -> bool:
        """训练LightGBM模型"""
        try:
            df = self.features_data[symbol].copy()
            train_data = df[df.index <= train_end_date]

            # 选择特征列
            feature_columns = [
                'returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20',
                'rsi_14', 'bb_position', 'volume_ratio', 'volatility',
                'momentum_5', 'price_strength'
            ]

            # 确保所有特征列都存在
            available_features = [col for col in feature_columns if col in train_data.columns]

            if len(available_features) < 5:
                self.logger.warning(f"    ⚠️ {symbol}: 可用特征不足")
                return False

            # 准备数据
            X = train_data[available_features].values
            y = train_data['target_direction'].values

            # 移除包含NaN的行
            valid_indices = ~np.isnan(X).any(axis=1) & ~np.isnan(y)
            X = X[valid_indices]
            y = y[valid_indices]

            if len(X) < 100:
                self.logger.warning(f"    ⚠️ {symbol}: 训练数据不足 ({len(X)} 个样本)")
                return False

            # 数据标准化
            if symbol not in self.scalers:
                self.scalers[symbol] = StandardScaler()

            X_scaled = self.scalers[symbol].fit_transform(X)

            # 根据市场波动率调整模型参数
            if self.market_volatility > 0.03:  # 高波动市场
                lgb_params = {
                    'objective': 'binary',
                    'metric': 'binary_logloss',
                    'boosting_type': 'gbdt',
                    'num_leaves': 10,  # 更保守
                    'learning_rate': 0.05,
                    'feature_fraction': 0.7,
                    'verbose': -1,
                    'random_state': 42,
                    'n_jobs': 1,
                    'max_depth': 3,
                    'min_child_samples': 100,
                }
            else:  # 低波动市场
                lgb_params = {
                    'objective': 'binary',
                    'metric': 'binary_logloss',
                    'boosting_type': 'gbdt',
                    'num_leaves': 15,
                    'learning_rate': 0.1,
                    'feature_fraction': 0.8,
                    'verbose': -1,
                    'random_state': 42,
                    'n_jobs': 1,
                    'max_depth': 4,
                    'min_child_samples': 50,
                }

            # 训练模型
            train_data_lgb = lgb.Dataset(X_scaled, label=y)
            model = lgb.train(
                lgb_params,
                train_data_lgb,
                num_boost_round=50,
                callbacks=[lgb.log_evaluation(0)]
            )

            # 保存模型
            self.models[symbol] = model

            # 计算准确率
            y_pred = (model.predict(X_scaled) > 0.5).astype(int)
            accuracy = accuracy_score(y, y_pred)

            self.logger.info(f"    ✅ {symbol}: 训练完成，准确率: {accuracy:.3f}")
            return True

        except Exception as e:
            self.logger.warning(f"    ❌ {symbol}: 训练失败: {str(e)}")
            return False

    def optimized_risk_management(self, portfolio: Dict, current_date: str) -> bool:
        """优化的风险管理（返回是否可以交易）"""
        try:
            current_drawdown = portfolio['current_drawdown']

            # 如果当前处于止损状态
            if self.stop_loss_triggered:
                self.days_in_stop_loss += 1

                # 检查恢复条件
                recovery_conditions = [
                    current_drawdown < self.recovery_threshold,  # 回撤降低到8%以下
                    self.days_in_stop_loss >= self.max_stop_loss_days,  # 或者已经止损30天
                    self.market_trend == 1 and current_drawdown < 0.10  # 或者市场转为上涨且回撤<10%
                ]

                if any(recovery_conditions):
                    self.logger.info(f"🔄 恢复交易条件满足:")
                    self.logger.info(f"   - 当前回撤: {current_drawdown:.2%}")
                    self.logger.info(f"   - 止损天数: {self.days_in_stop_loss}")
                    self.logger.info(f"   - 市场趋势: {self.market_trend}")

                    self.stop_loss_triggered = False
                    self.days_in_stop_loss = 0
                    self.consecutive_losses = 0

                    # 恢复时使用更保守的参数
                    self.single_position_limit = min(0.15, self.single_position_limit)
                    self.max_positions = min(2, self.max_positions)

                    self.logger.info(f"📈 恢复交易，调整参数:")
                    self.logger.info(f"   - 单仓位限制: {self.single_position_limit:.1%}")
                    self.logger.info(f"   - 最大持仓: {self.max_positions}")

                    return True
                else:
                    return False  # 继续止损

            # 检查是否需要触发止损
            if current_drawdown > self.stop_loss_threshold:
                self.logger.warning(f"🛑 触发止损！")
                self.logger.warning(f"   - 当前回撤: {current_drawdown:.2%}")
                self.logger.warning(f"   - 止损阈值: {self.stop_loss_threshold:.2%}")

                self.stop_loss_triggered = True
                self.stop_loss_trigger_date = current_date
                self.days_in_stop_loss = 0

                # 清空所有持仓
                for symbol in list(portfolio['positions'].keys()):
                    if symbol in self.features_data and current_date in self.features_data[symbol].index:
                        current_price = self.features_data[symbol].loc[current_date, 'close']
                        shares = portfolio['positions'][symbol]
                        portfolio['cash'] += shares * current_price

                portfolio['positions'] = {}

                return False

            return True  # 可以正常交易

        except Exception as e:
            self.logger.warning(f"    ⚠️ 风险管理失败: {str(e)}")
            return True

    def predict_next_day(self, symbol: str, current_date: str) -> Tuple[float, float]:
        """预测次日涨跌方向和概率"""
        try:
            if symbol not in self.models:
                return 0.5, 0.0

            df = self.features_data[symbol].copy()
            current_data = df[df.index <= current_date].iloc[-1:]

            # 选择特征列
            feature_columns = [
                'returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20',
                'rsi_14', 'bb_position', 'volume_ratio', 'volatility',
                'momentum_5', 'price_strength'
            ]

            available_features = [col for col in feature_columns if col in df.columns]
            X = current_data[available_features].values

            if np.isnan(X).any():
                return 0.5, 0.0

            # 标准化
            X_scaled = self.scalers[symbol].transform(X)

            # 预测
            prob = self.models[symbol].predict(X_scaled)[0]

            # 根据市场状态调整预测阈值
            if self.market_volatility > 0.03:  # 高波动市场，更保守
                confidence_threshold = 0.6
            else:
                confidence_threshold = 0.55

            # 估算预测收益率
            if prob > confidence_threshold:
                predicted_return = 0.015  # 1.5%
            elif prob < (1 - confidence_threshold):
                predicted_return = -0.015  # -1.5%
            else:
                predicted_return = 0.0  # 中性

            return prob, predicted_return

        except Exception as e:
            self.logger.warning(f"    ⚠️ {symbol} 预测失败: {str(e)}")
            return 0.5, 0.0

    def run_backtest(self) -> bool:
        """运行优化版回测"""
        self.logger.info("=" * 60)
        self.logger.info("第3阶段：优化版模型训练与回测")
        self.logger.info("=" * 60)

        try:
            # 初始化组合
            portfolio = {
                'cash': self.initial_capital,
                'positions': {},
                'total_value': self.initial_capital,
                'daily_returns': [],
                'peak_value': self.initial_capital,
                'current_drawdown': 0.0,
                'max_drawdown': 0.0
            }

            # 获取所有交易日期
            all_dates = set()
            for symbol in self.crypto_symbols:
                all_dates.update(self.features_data[symbol].index)
            all_dates = sorted(list(all_dates))

            # 确保有足够的历史数据用于初始训练
            min_training_days = 100
            if len(all_dates) < min_training_days + 30:
                self.logger.error(f"❌ 数据不足，需要至少 {min_training_days + 30} 天数据")
                return False

            start_backtest_idx = min_training_days
            backtest_dates = all_dates[start_backtest_idx:]

            self.logger.info(f"📊 回测期间: {backtest_dates[0]} 至 {backtest_dates[-1]}")
            self.logger.info(f"📈 回测天数: {len(backtest_dates)} 天")

            # 每日回测循环
            for i, current_date in enumerate(backtest_dates):
                if i % 100 == 0:
                    progress = (i / len(backtest_dates)) * 100
                    self.logger.info(f"  回测进度: {progress:.1f}% ({current_date})")

                # 评估市场状态
                market_condition = self.assess_market_condition(current_date)

                # 风险管理检查
                can_trade = self.optimized_risk_management(portfolio, current_date)

                if not can_trade:
                    # 更新组合价值但不交易
                    self.update_portfolio_value_only(portfolio, current_date)
                    continue

                # 每两周重新训练模型
                if i % 14 == 0 or i == 0:
                    train_end_date = all_dates[start_backtest_idx + i - 1] if i > 0 else all_dates[start_backtest_idx - 1]

                    successful_models = 0
                    for symbol in self.crypto_symbols:
                        if self.train_model(symbol, train_end_date):
                            successful_models += 1

                    if successful_models < 3:
                        self.logger.warning(f"⚠️ 可用模型不足: {successful_models}")
                        self.update_portfolio_value_only(portfolio, current_date)
                        continue

                # 获取当日预测
                predictions = {}
                valid_predictions = 0

                for symbol in self.crypto_symbols:
                    if symbol in self.models:
                        prob, pred_return = self.predict_next_day(symbol, current_date)
                        if prob != 0.5:
                            predictions[symbol] = {
                                'probability': prob,
                                'predicted_return': pred_return,
                                'direction': 1 if prob > 0.55 else (-1 if prob < 0.45 else 0)
                            }
                            if predictions[symbol]['direction'] != 0:
                                valid_predictions += 1

                # 如果有效预测不足，跳过交易
                if valid_predictions < 2:
                    self.update_portfolio_value_only(portfolio, current_date)
                    continue

                # 选择交易标的
                high_confidence_predictions = {
                    symbol: pred for symbol, pred in predictions.items()
                    if abs(pred['probability'] - 0.5) > 0.1
                }

                if not high_confidence_predictions:
                    self.update_portfolio_value_only(portfolio, current_date)
                    continue

                # 按预测收益率排序
                sorted_predictions = sorted(
                    high_confidence_predictions.items(),
                    key=lambda x: abs(x[1]['predicted_return']),
                    reverse=True
                )

                # 根据市场状态调整选择数量
                if self.market_volatility > 0.03:
                    max_selections = min(2, self.max_positions)  # 高波动时减少持仓
                else:
                    max_selections = self.max_positions

                selected_symbols = [item[0] for item in sorted_predictions[:max_selections]]

                # 执行交易
                self.execute_trades_optimized(portfolio, selected_symbols, predictions, current_date)

                # 更新组合价值
                self.update_portfolio_value_only(portfolio, current_date)

            self.logger.info("✅ 优化版回测完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 回测失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    def execute_trades_optimized(self, portfolio: Dict, selected_symbols: List[str],
                                predictions: Dict, current_date: str):
        """执行交易（优化版）"""
        try:
            # 清空当前持仓
            for symbol in list(portfolio['positions'].keys()):
                if symbol in self.features_data and current_date in self.features_data[symbol].index:
                    current_price = self.features_data[symbol].loc[current_date, 'close']
                    shares = portfolio['positions'][symbol]
                    sell_value = shares * current_price
                    portfolio['cash'] += sell_value

            portfolio['positions'] = {}

            # 根据市场状态调整仓位
            if self.market_volatility > 0.03:
                max_allocation = 0.6  # 高波动时最多60%仓位
            else:
                max_allocation = 0.8  # 低波动时最多80%仓位

            # 计算每个标的的分配资金
            if selected_symbols:
                total_allocation = min(max_allocation, len(selected_symbols) * self.single_position_limit)
                allocation_per_symbol = (portfolio['cash'] * total_allocation) / len(selected_symbols)

                for symbol in selected_symbols:
                    if (symbol in self.features_data and
                        current_date in self.features_data[symbol].index and
                        symbol in predictions):

                        current_price = self.features_data[symbol].loc[current_date, 'close']
                        prediction = predictions[symbol]

                        if current_price > 0 and prediction['direction'] == 1:  # 只做多头
                            shares = allocation_per_symbol / current_price
                            portfolio['positions'][symbol] = shares
                            portfolio['cash'] -= shares * current_price

        except Exception as e:
            self.logger.warning(f"    ⚠️ 交易执行失败: {str(e)}")

    def update_portfolio_value_only(self, portfolio: Dict, current_date: str):
        """更新组合价值"""
        try:
            positions_value = 0

            for symbol, shares in portfolio['positions'].items():
                if symbol in self.features_data and current_date in self.features_data[symbol].index:
                    current_price = self.features_data[symbol].loc[current_date, 'close']
                    positions_value += shares * current_price

            previous_value = portfolio['total_value']
            portfolio['total_value'] = portfolio['cash'] + positions_value

            # 计算日收益率
            if previous_value > 0:
                daily_return = (portfolio['total_value'] - previous_value) / previous_value
                portfolio['daily_returns'].append(daily_return)
            else:
                portfolio['daily_returns'].append(0)

            # 更新回撤计算
            if portfolio['total_value'] > portfolio['peak_value']:
                portfolio['peak_value'] = portfolio['total_value']
                portfolio['current_drawdown'] = 0.0
            else:
                portfolio['current_drawdown'] = (portfolio['peak_value'] - portfolio['total_value']) / portfolio['peak_value']
                portfolio['max_drawdown'] = max(portfolio['max_drawdown'], portfolio['current_drawdown'])

            # 记录组合历史
            self.portfolio_history.append({
                'date': current_date,
                'total_value': portfolio['total_value'],
                'cash': portfolio['cash'],
                'positions': portfolio['positions'].copy(),
                'daily_return': portfolio['daily_returns'][-1],
                'current_drawdown': portfolio['current_drawdown'],
                'max_drawdown': portfolio['max_drawdown'],
                'stop_loss_active': self.stop_loss_triggered,
                'market_trend': self.market_trend,
                'market_volatility': self.market_volatility
            })

        except Exception as e:
            self.logger.warning(f"    ⚠️ 组合价值更新失败: {str(e)}")

    def calculate_performance_metrics(self) -> Dict:
        """计算性能指标"""
        self.logger.info("=" * 60)
        self.logger.info("第4阶段：性能分析")
        self.logger.info("=" * 60)

        try:
            if not self.portfolio_history:
                return {}

            df = pd.DataFrame(self.portfolio_history)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)

            # 基础指标
            initial_value = self.initial_capital
            final_value = df['total_value'].iloc[-1]
            total_return = (final_value - initial_value) / initial_value

            # 时间相关指标
            trading_days = len(df)
            years = trading_days / 252

            # 年化收益率
            annual_return = (final_value / initial_value) ** (1 / years) - 1 if years > 0 else 0

            # 日收益率统计
            daily_returns = df['daily_return'].dropna()

            # 夏普比率
            risk_free_rate = 0.03
            excess_returns = daily_returns - risk_free_rate / 252
            sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0

            # 最大回撤
            max_drawdown = df['max_drawdown'].max()

            # 胜率
            win_rate = (daily_returns > 0).mean()

            # 止损统计
            stop_loss_days = (df['stop_loss_active'] == True).sum()
            stop_loss_ratio = stop_loss_days / len(df)

            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'trading_days': trading_days,
                'final_value': final_value,
                'stop_loss_days': stop_loss_days,
                'stop_loss_ratio': stop_loss_ratio,
                'years': years
            }

            self.performance_metrics = metrics

            # 打印性能报告
            self.logger.info("📊 优化版回测性能报告")
            self.logger.info("-" * 40)
            self.logger.info(f"总收益率:        {total_return:.2%}")
            self.logger.info(f"年化收益率:      {annual_return:.2%}")
            self.logger.info(f"夏普比率:        {sharpe_ratio:.3f}")
            self.logger.info(f"最大回撤:        {max_drawdown:.2%}")
            self.logger.info(f"胜率:            {win_rate:.2%}")
            self.logger.info(f"最终资产:        ${final_value:,.2f}")
            self.logger.info(f"止损天数:        {stop_loss_days} 天 ({stop_loss_ratio:.1%})")

            return metrics

        except Exception as e:
            self.logger.error(f"❌ 性能分析失败: {str(e)}")
            return {}

    def run_complete_backtest(self) -> bool:
        """运行完整的回测流程"""
        try:
            self.logger.info("🚀 开始运行优化版加密货币量化交易回测系统")
            self.logger.info("=" * 80)

            # 加载数据
            if not self.load_local_data():
                return False

            # 特征工程
            if not self.create_features():
                return False

            # 运行回测
            if not self.run_backtest():
                return False

            # 性能分析
            metrics = self.calculate_performance_metrics()
            if not metrics:
                return False

            self.logger.info("=" * 80)
            self.logger.info("🎉 优化版回测完成！")

            return True

        except Exception as e:
            self.logger.error(f"❌ 回测系统运行失败: {str(e)}")
            return False


def main():
    """主函数"""
    try:
        print("🔧 优化版加密货币量化交易回测系统")
        print("=" * 60)
        print("优化内容:")
        print("- 改进止损恢复机制")
        print("- 动态调整交易参数")
        print("- 增加市场状态判断")
        print("- 优化风险管理策略")
        print("=" * 60)

        # 创建回测实例
        backtest = OptimizedCryptoBacktest(
            start_date="2020-01-01",
            end_date="2024-12-31",
            initial_capital=100000.0,
            max_positions=3,
            single_position_limit=0.25,
            stop_loss_threshold=0.12
        )

        # 运行完整回测
        success = backtest.run_complete_backtest()

        if success:
            print("\n✅ 优化版回测成功完成！")
            sys.exit(0)
        else:
            print("\n❌ 回测失败！")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
