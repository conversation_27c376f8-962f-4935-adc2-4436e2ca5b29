#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版加密货币量化交易策略

基于前一版本的问题分析，进行以下改进：
1. 降低交易频率（每周调仓）
2. 增加特征数量和质量
3. 改进风险控制机制
4. 优化模型训练策略
5. 加强交易成本控制

作者：加密货币量化交易系统
日期：2025年1月29日
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import os
import json
import warnings
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import sys

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedCryptoStrategy:
    """改进版加密货币量化交易策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_file = "daily_body_changes/combined_data/all_contracts_body_changes.csv"
        self.output_dir = "improved_strategy_results"
        self.start_date = "2022-01-01"
        self.end_date = "2025-07-29"
        
        # 改进的策略参数
        self.long_positions = 3  # 减少持仓数量
        self.short_positions = 3
        self.prediction_horizon = 2
        self.transaction_cost = 0.0005  # 降低交易成本假设
        self.rebalance_freq = 5  # 每5天调仓一次（每周）
        self.min_data_points = 120  # 增加最小数据要求
        self.confidence_threshold = 0.1  # 预测置信度阈值
        
        # 风险控制参数
        self.max_position_size = 0.15  # 单个币种最大仓位15%
        self.stop_loss = -0.05  # 5%止损
        self.max_daily_loss = -0.03  # 单日最大亏损3%
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "reports"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "charts"), exist_ok=True)
        
        # 数据存储
        self.data = None
        self.predictions = []
        self.portfolio_records = []
        
        print("🚀 改进版加密货币量化交易策略")
        print("=" * 60)
        print(f"📅 回测期间: {self.start_date} ~ {self.end_date}")
        print(f"🎯 策略: 做多{self.long_positions}个，做空{self.short_positions}个")
        print(f"⏰ T+{self.prediction_horizon}预测，每{self.rebalance_freq}天调仓")
        print(f"🛡️ 风险控制: 单仓位≤{self.max_position_size*100}%，止损{self.stop_loss*100}%")
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        try:
            print(f"\n📊 加载数据...")
            
            # 读取数据
            self.data = pd.read_csv(self.data_file)
            self.data['date'] = pd.to_datetime(self.data['date'])
            
            # 筛选时间范围
            start_dt = pd.to_datetime(self.start_date)
            end_dt = pd.to_datetime(self.end_date)
            self.data = self.data[(self.data['date'] >= start_dt) & (self.data['date'] <= end_dt)]
            
            # 筛选有足够数据的币种
            symbol_counts = self.data['symbol'].value_counts()
            valid_symbols = symbol_counts[symbol_counts >= self.min_data_points].index
            self.data = self.data[self.data['symbol'].isin(valid_symbols)]
            
            print(f"✅ 数据加载完成")
            print(f"📊 数据量: {len(self.data):,} 条")
            print(f"🪙 有效币种: {self.data['symbol'].nunique()}")
            print(f"📅 时间范围: {self.data['date'].min().date()} ~ {self.data['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def create_enhanced_features(self):
        """创建增强特征"""
        try:
            print(f"\n🔧 创建增强特征...")
            
            feature_data = []
            symbols = self.data['symbol'].unique()
            
            for i, symbol in enumerate(symbols):
                if i % 50 == 0:
                    print(f"  处理进度: {i+1}/{len(symbols)} ({(i+1)/len(symbols)*100:.1f}%)")
                
                symbol_data = self.data[self.data['symbol'] == symbol].copy()
                symbol_data = symbol_data.sort_values('date').reset_index(drop=True)
                
                if len(symbol_data) < self.min_data_points:
                    continue
                
                # 基础特征
                for period in [3, 5, 10, 20, 30]:
                    # 实体涨跌幅特征
                    symbol_data[f'body_ma_{period}'] = symbol_data['body_change_pct'].rolling(period, min_periods=1).mean()
                    symbol_data[f'body_std_{period}'] = symbol_data['body_change_pct'].rolling(period, min_periods=1).std()
                    symbol_data[f'body_skew_{period}'] = symbol_data['body_change_pct'].rolling(period, min_periods=3).skew()
                    
                    # 价格特征
                    symbol_data[f'close_ma_{period}'] = symbol_data['close'].rolling(period, min_periods=1).mean()
                    symbol_data[f'price_ratio_{period}'] = symbol_data['close'] / symbol_data[f'close_ma_{period}'] - 1
                    symbol_data[f'price_std_{period}'] = symbol_data['close'].rolling(period, min_periods=1).std()
                    
                    # 收益率特征
                    symbol_data[f'return_{period}'] = symbol_data['close'].pct_change(period)
                    symbol_data[f'return_ma_{period}'] = symbol_data[f'return_{period}'].rolling(period, min_periods=1).mean()
                    
                    # 波动率特征
                    symbol_data[f'volatility_{period}'] = symbol_data['close'].pct_change().rolling(period, min_periods=1).std()
                
                # 技术指标
                # RSI
                for period in [14, 30]:
                    delta = symbol_data['close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(period, min_periods=1).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(period, min_periods=1).mean()
                    rs = gain / (loss + 1e-8)
                    symbol_data[f'rsi_{period}'] = 100 - (100 / (1 + rs))
                
                # MACD
                ema12 = symbol_data['close'].ewm(span=12).mean()
                ema26 = symbol_data['close'].ewm(span=26).mean()
                symbol_data['macd'] = ema12 - ema26
                symbol_data['macd_signal'] = symbol_data['macd'].ewm(span=9).mean()
                symbol_data['macd_histogram'] = symbol_data['macd'] - symbol_data['macd_signal']
                
                # 布林带
                for period in [20, 30]:
                    ma = symbol_data['close'].rolling(period, min_periods=1).mean()
                    std = symbol_data['close'].rolling(period, min_periods=1).std()
                    symbol_data[f'bb_upper_{period}'] = ma + 2 * std
                    symbol_data[f'bb_lower_{period}'] = ma - 2 * std
                    symbol_data[f'bb_position_{period}'] = (symbol_data['close'] - symbol_data[f'bb_lower_{period}']) / \
                                                          (symbol_data[f'bb_upper_{period}'] - symbol_data[f'bb_lower_{period}'] + 1e-8)
                
                # 趋势特征
                for period in [5, 10, 20]:
                    # 线性回归斜率
                    def calc_slope(series):
                        if len(series) < 2:
                            return 0
                        x = np.arange(len(series))
                        return np.polyfit(x, series, 1)[0] if not series.isna().all() else 0
                    
                    symbol_data[f'trend_slope_{period}'] = symbol_data['close'].rolling(period, min_periods=2).apply(calc_slope)
                    
                    # 价格位置
                    symbol_data[f'price_position_{period}'] = (symbol_data['close'] - symbol_data['low'].rolling(period, min_periods=1).min()) / \
                                                             (symbol_data['high'].rolling(period, min_periods=1).max() - symbol_data['low'].rolling(period, min_periods=1).min() + 1e-8)
                
                # 成交量特征
                for period in [5, 10, 20]:
                    symbol_data[f'volume_ma_{period}'] = symbol_data['volume'].rolling(period, min_periods=1).mean()
                    symbol_data[f'volume_ratio_{period}'] = symbol_data['volume'] / (symbol_data[f'volume_ma_{period}'] + 1e-8)
                
                # 目标变量
                symbol_data['target'] = symbol_data['body_change_pct'].shift(-self.prediction_horizon)
                
                # 删除缺失的目标变量
                symbol_data = symbol_data.dropna(subset=['target'])
                
                if len(symbol_data) > 0:
                    feature_data.append(symbol_data)
            
            if not feature_data:
                print("❌ 没有有效的特征数据")
                return False
            
            # 合并所有数据
            self.data = pd.concat(feature_data, ignore_index=True)
            self.data = self.data.sort_values(['date', 'symbol']).reset_index(drop=True)
            
            # 定义特征列（排除基础数据列）
            exclude_cols = ['timestamp', 'date', 'symbol', 'target', 'candle_type', 'open', 'high', 'low', 'close', 'volume']
            self.feature_columns = [col for col in self.data.columns if col not in exclude_cols]
            
            print(f"✅ 增强特征创建完成")
            print(f"📊 有效数据: {len(self.data):,} 条")
            print(f"🔢 特征数量: {len(self.feature_columns)}")
            
            return True

        except Exception as e:
            print(f"❌ 特征创建失败: {str(e)}")
            return False

    def improved_rolling_prediction(self):
        """改进的滚动预测"""
        try:
            print(f"\n🤖 开始改进滚动预测...")

            # 获取所有交易日期
            all_dates = sorted(self.data['date'].unique())

            # 设置预测参数
            prediction_start_idx = self.min_data_points
            total_dates = len(all_dates) - prediction_start_idx

            print(f"📊 预测设置:")
            print(f"  总交易日: {len(all_dates)}")
            print(f"  预测开始: 第{prediction_start_idx+1}天")
            print(f"  预测天数: {total_dates}")
            print(f"  调仓频率: 每{self.rebalance_freq}天")

            # 改进的LightGBM参数
            lgb_params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 63,  # 增加复杂度
                'learning_rate': 0.05,  # 降低学习率
                'feature_fraction': 0.9,
                'bagging_fraction': 0.9,
                'bagging_freq': 5,
                'min_data_in_leaf': 20,  # 增加最小叶子节点数据
                'lambda_l1': 0.1,  # L1正则化
                'lambda_l2': 0.1,  # L2正则化
                'verbose': -1,
                'random_state': 42
            }

            # 每周重新训练模型
            for i, current_date in enumerate(all_dates[prediction_start_idx:], prediction_start_idx):
                if i % 30 == 0:
                    progress = (i - prediction_start_idx + 1) / total_dates * 100
                    print(f"  预测进度: {i-prediction_start_idx+1}/{total_dates} ({progress:.1f}%)")

                # 每周重新训练模型
                if (i - prediction_start_idx) % self.rebalance_freq == 0:
                    # 准备训练数据（使用过去120天的数据）
                    train_start_idx = max(0, i - 120)
                    train_dates = all_dates[train_start_idx:i]

                    train_data = self.data[self.data['date'].isin(train_dates)]

                    if len(train_data) < 100:
                        continue

                    # 准备特征和目标
                    X_train = train_data[self.feature_columns].fillna(0)
                    y_train = train_data['target'].fillna(0)

                    # 特征选择（选择重要特征）
                    if len(X_train) > 1000:  # 只有足够数据时才进行特征选择
                        # 简单的方差筛选
                        feature_var = X_train.var()
                        selected_features = feature_var[feature_var > 0.01].index.tolist()
                        if len(selected_features) > 10:
                            X_train = X_train[selected_features]
                            self.current_features = selected_features
                        else:
                            self.current_features = self.feature_columns
                    else:
                        self.current_features = self.feature_columns

                    # 训练模型
                    train_dataset = lgb.Dataset(X_train, label=y_train)
                    self.current_model = lgb.train(
                        lgb_params,
                        train_dataset,
                        num_boost_round=100,
                        callbacks=[lgb.log_evaluation(0)]
                    )

                # 预测当日数据
                pred_data = self.data[self.data['date'] == current_date]

                if len(pred_data) == 0 or not hasattr(self, 'current_model'):
                    continue

                # 使用当前特征进行预测
                X_pred = pred_data[self.current_features].fillna(0)
                y_pred = self.current_model.predict(X_pred)

                # 保存预测结果
                for j, (idx, row) in enumerate(pred_data.iterrows()):
                    self.predictions.append({
                        'date': current_date,
                        'symbol': row['symbol'],
                        'predicted_return': y_pred[j],
                        'actual_return': row['target'],
                        'confidence': abs(y_pred[j])  # 简单的置信度度量
                    })

            print(f"✅ 改进滚动预测完成")
            print(f"📊 预测记录: {len(self.predictions):,} 条")

            return True

        except Exception as e:
            print(f"❌ 滚动预测失败: {str(e)}")
            return False

    def execute_improved_strategy(self):
        """执行改进的交易策略"""
        try:
            print(f"\n💼 执行改进交易策略...")

            # 转换预测为DataFrame
            pred_df = pd.DataFrame(self.predictions)

            if pred_df.empty:
                print("❌ 没有预测数据")
                return False

            # 按日期分组执行策略
            trading_dates = sorted(pred_df['date'].unique())
            current_positions = {}
            portfolio_value = 1.0  # 初始资金

            print(f"📊 改进策略设置:")
            print(f"  交易天数: {len(trading_dates)}")
            print(f"  做多数量: {self.long_positions}")
            print(f"  做空数量: {self.short_positions}")
            print(f"  调仓频率: 每{self.rebalance_freq}天")
            print(f"  置信度阈值: {self.confidence_threshold}")

            for i, trade_date in enumerate(trading_dates):
                if i % 100 == 0:
                    print(f"  策略进度: {i+1}/{len(trading_dates)} ({(i+1)/len(trading_dates)*100:.1f}%)")

                daily_pred = pred_df[pred_df['date'] == trade_date].copy()

                if len(daily_pred) < self.long_positions + self.short_positions:
                    continue

                # 筛选高置信度预测
                high_confidence = daily_pred[daily_pred['confidence'] >= self.confidence_threshold]

                if len(high_confidence) < self.long_positions + self.short_positions:
                    # 如果高置信度预测不足，使用所有预测
                    high_confidence = daily_pred

                # 按预测收益率排序选币
                high_confidence = high_confidence.sort_values('predicted_return', ascending=False)

                # 选择做多和做空的币种
                long_symbols = high_confidence.head(self.long_positions)['symbol'].tolist()
                short_symbols = high_confidence.tail(self.short_positions)['symbol'].tolist()

                # 计算当日收益
                daily_return = 0.0
                transaction_costs = 0.0

                # 计算持仓收益
                for symbol, weight in current_positions.items():
                    symbol_data = daily_pred[daily_pred['symbol'] == symbol]
                    if not symbol_data.empty and not pd.isna(symbol_data['actual_return'].iloc[0]):
                        actual_return = symbol_data['actual_return'].iloc[0]
                        daily_return += weight * actual_return / 100

                # 风险控制：检查单日亏损
                if daily_return < self.max_daily_loss:
                    # 触发风险控制，减少仓位
                    position_scale = 0.5
                else:
                    position_scale = 1.0

                # 只在调仓日更新持仓
                if i % self.rebalance_freq == 0:
                    # 更新持仓
                    new_positions = {}
                    long_weight = (self.max_position_size * position_scale) if self.long_positions > 0 else 0
                    short_weight = -(self.max_position_size * position_scale) if self.short_positions > 0 else 0

                    for symbol in long_symbols:
                        new_positions[symbol] = long_weight
                    for symbol in short_symbols:
                        new_positions[symbol] = short_weight

                    # 计算调仓成本
                    all_symbols = set(list(current_positions.keys()) + list(new_positions.keys()))
                    for symbol in all_symbols:
                        old_weight = current_positions.get(symbol, 0)
                        new_weight = new_positions.get(symbol, 0)
                        if abs(new_weight - old_weight) > 1e-6:
                            transaction_costs += abs(new_weight - old_weight) * self.transaction_cost

                    current_positions = new_positions.copy()

                # 更新组合价值
                portfolio_value *= (1 + daily_return - transaction_costs)

                # 记录组合表现
                self.portfolio_records.append({
                    'date': trade_date,
                    'gross_return': daily_return,
                    'transaction_cost': transaction_costs,
                    'net_return': daily_return - transaction_costs,
                    'portfolio_value': portfolio_value,
                    'long_symbols': ','.join(long_symbols),
                    'short_symbols': ','.join(short_symbols),
                    'num_positions': len(current_positions),
                    'position_scale': position_scale
                })

            print(f"✅ 改进策略执行完成")
            print(f"📊 交易记录: {len(self.portfolio_records)} 条")
            print(f"💰 最终组合价值: {portfolio_value:.4f}")

            return True

        except Exception as e:
            print(f"❌ 策略执行失败: {str(e)}")
            return False

    def calculate_improved_metrics(self):
        """计算改进的性能指标"""
        try:
            if not self.portfolio_records:
                return {}

            portfolio_df = pd.DataFrame(self.portfolio_records)
            returns = portfolio_df['net_return']

            # 计算累计收益
            portfolio_df['cumulative_return'] = portfolio_df['portfolio_value']

            # 基础指标
            total_return = (portfolio_df['portfolio_value'].iloc[-1] - 1) * 100
            trading_days = len(returns)
            annual_return = ((portfolio_df['portfolio_value'].iloc[-1]) ** (252 / trading_days) - 1) * 100

            # 风险指标
            volatility = returns.std() * np.sqrt(252) * 100
            sharpe_ratio = (annual_return / 100) / (volatility / 100) if volatility > 0 else 0

            # 最大回撤
            peak = portfolio_df['portfolio_value'].expanding().max()
            drawdown = (portfolio_df['portfolio_value'] - peak) / peak
            max_drawdown = drawdown.min() * 100

            # 胜率和盈亏比
            win_rate = (returns > 0).mean() * 100
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]
            avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
            avg_loss = negative_returns.mean() if len(negative_returns) > 0 else 0
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

            # 改进的指标
            # 信息比率
            excess_returns = returns - returns.mean()
            information_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0

            # 卡尔马比率
            calmar_ratio = (annual_return / 100) / abs(max_drawdown / 100) if max_drawdown != 0 else 0

            # 交易效率
            total_transaction_cost = portfolio_df['transaction_cost'].sum() * 100
            trade_efficiency = total_return / total_transaction_cost if total_transaction_cost > 0 else 0

            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'information_ratio': information_ratio,
                'calmar_ratio': calmar_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trading_days': trading_days,
                'avg_daily_return': returns.mean() * 100,
                'total_transaction_cost': total_transaction_cost,
                'trade_efficiency': trade_efficiency,
                'best_day': returns.max() * 100,
                'worst_day': returns.min() * 100,
                'final_portfolio_value': portfolio_df['portfolio_value'].iloc[-1]
            }

            return metrics, portfolio_df

        except Exception as e:
            print(f"❌ 计算性能指标失败: {str(e)}")
            return {}, None

    def create_improved_charts(self, portfolio_df):
        """创建改进的图表"""
        try:
            print(f"\n📊 创建改进图表...")

            # 创建图表
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('改进版加密货币量化交易策略回测结果', fontsize=16, fontweight='bold')

            # 1. 组合价值曲线
            ax1 = axes[0, 0]
            ax1.plot(portfolio_df['date'], portfolio_df['portfolio_value'],
                    linewidth=2, color='blue', label='组合价值')
            ax1.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='初始价值')
            ax1.set_title('组合价值曲线', fontweight='bold')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('组合价值')
            ax1.grid(True, alpha=0.3)
            ax1.legend()

            # 2. 回撤曲线
            ax2 = axes[0, 1]
            peak = portfolio_df['portfolio_value'].expanding().max()
            drawdown = (portfolio_df['portfolio_value'] - peak) / peak * 100
            ax2.fill_between(portfolio_df['date'], drawdown, 0, color='red', alpha=0.3)
            ax2.plot(portfolio_df['date'], drawdown, color='red', linewidth=1)
            ax2.set_title('回撤曲线', fontweight='bold')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('回撤 (%)')
            ax2.grid(True, alpha=0.3)

            # 3. 日收益率分布
            ax3 = axes[0, 2]
            daily_returns = portfolio_df['net_return'] * 100
            ax3.hist(daily_returns, bins=50, alpha=0.7, color='green', edgecolor='black')
            ax3.axvline(daily_returns.mean(), color='red', linestyle='--',
                       label=f'均值: {daily_returns.mean():.3f}%')
            ax3.set_title('日收益率分布', fontweight='bold')
            ax3.set_xlabel('日收益率 (%)')
            ax3.set_ylabel('频次')
            ax3.grid(True, alpha=0.3)
            ax3.legend()

            # 4. 滚动夏普比率
            ax4 = axes[1, 0]
            window = 30
            rolling_returns = portfolio_df['net_return'].rolling(window)
            rolling_sharpe = (rolling_returns.mean() / rolling_returns.std()) * np.sqrt(252)
            ax4.plot(portfolio_df['date'][window-1:], rolling_sharpe[window-1:],
                    linewidth=2, color='purple', label=f'{window}日滚动夏普比率')
            ax4.axhline(0, color='black', linestyle='-', alpha=0.3)
            ax4.set_title('滚动夏普比率', fontweight='bold')
            ax4.set_xlabel('日期')
            ax4.set_ylabel('夏普比率')
            ax4.grid(True, alpha=0.3)
            ax4.legend()

            # 5. 持仓数量变化
            ax5 = axes[1, 1]
            ax5.plot(portfolio_df['date'], portfolio_df['num_positions'],
                    linewidth=2, color='orange', label='持仓数量')
            ax5.set_title('持仓数量变化', fontweight='bold')
            ax5.set_xlabel('日期')
            ax5.set_ylabel('持仓数量')
            ax5.grid(True, alpha=0.3)
            ax5.legend()

            # 6. 交易成本累计
            ax6 = axes[1, 2]
            cumulative_cost = portfolio_df['transaction_cost'].cumsum() * 100
            ax6.plot(portfolio_df['date'], cumulative_cost,
                    linewidth=2, color='brown', label='累计交易成本')
            ax6.set_title('累计交易成本', fontweight='bold')
            ax6.set_xlabel('日期')
            ax6.set_ylabel('累计成本 (%)')
            ax6.grid(True, alpha=0.3)
            ax6.legend()

            plt.tight_layout()

            # 保存图表
            chart_file = os.path.join(self.output_dir, "charts", "improved_strategy_charts.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 改进图表已保存: {chart_file}")

            return True

        except Exception as e:
            print(f"❌ 创建图表失败: {str(e)}")
            return False

    def run_improved_strategy(self):
        """运行改进策略"""
        try:
            print("🚀 开始运行改进版加密货币量化交易策略...")
            print("=" * 60)

            # 1. 加载数据
            if not self.load_and_prepare_data():
                return False

            # 2. 创建增强特征
            if not self.create_enhanced_features():
                return False

            # 3. 改进滚动预测
            if not self.improved_rolling_prediction():
                return False

            # 4. 执行改进策略
            if not self.execute_improved_strategy():
                return False

            # 5. 计算性能
            metrics, portfolio_df = self.calculate_improved_metrics()

            if not metrics:
                print("❌ 无法计算性能指标")
                return False

            # 6. 创建图表
            self.create_improved_charts(portfolio_df)

            # 7. 保存结果
            self.save_improved_results(metrics, portfolio_df)

            # 8. 打印结果
            print(f"\n🎉 改进版策略回测完成！")
            print(f"📊 关键指标:")
            print(f"  💰 总收益率: {metrics['total_return']:.2f}%")
            print(f"  📈 年化收益率: {metrics['annual_return']:.2f}%")
            print(f"  📉 最大回撤: {metrics['max_drawdown']:.2f}%")
            print(f"  ⚡ 夏普比率: {metrics['sharpe_ratio']:.3f}")
            print(f"  📊 信息比率: {metrics['information_ratio']:.3f}")
            print(f"  🎯 胜率: {metrics['win_rate']:.1f}%")
            print(f"  💸 总交易成本: {metrics['total_transaction_cost']:.2f}%")
            print(f"  🔧 交易效率: {metrics['trade_efficiency']:.2f}")
            print(f"  📊 交易天数: {metrics['trading_days']}")
            print(f"  💎 最终价值: {metrics['final_portfolio_value']:.4f}")

            return True

        except Exception as e:
            print(f"❌ 改进策略失败: {str(e)}")
            return False

    def save_improved_results(self, metrics, portfolio_df):
        """保存改进结果"""
        try:
            # 保存组合数据
            portfolio_file = os.path.join(self.output_dir, "reports", "improved_portfolio.csv")
            portfolio_df.to_csv(portfolio_file, index=False, encoding='utf-8-sig')

            # 保存预测数据
            pred_file = os.path.join(self.output_dir, "reports", "improved_predictions.csv")
            pred_df = pd.DataFrame(self.predictions)
            pred_df.to_csv(pred_file, index=False, encoding='utf-8-sig')

            # 创建详细报告
            report = {
                'strategy_info': {
                    'name': '改进版加密货币量化交易策略',
                    'improvements': [
                        '降低交易频率（每5天调仓）',
                        '增加特征数量和质量',
                        '改进风险控制机制',
                        '优化模型训练策略',
                        '加强交易成本控制'
                    ],
                    'start_date': self.start_date,
                    'end_date': self.end_date,
                    'long_positions': self.long_positions,
                    'short_positions': self.short_positions,
                    'rebalance_freq': self.rebalance_freq,
                    'confidence_threshold': self.confidence_threshold,
                    'max_position_size': self.max_position_size,
                    'stop_loss': self.stop_loss
                },
                'performance_metrics': metrics,
                'data_info': {
                    'total_predictions': len(self.predictions),
                    'total_trades': len(self.portfolio_records),
                    'feature_count': len(self.feature_columns),
                    'data_points': len(self.data)
                },
                'generation_time': datetime.now().isoformat()
            }

            # 保存JSON报告
            report_file = os.path.join(self.output_dir, "reports", "improved_strategy_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"✅ 改进结果保存完成")
            print(f"📁 输出目录: {self.output_dir}")

        except Exception as e:
            print(f"❌ 保存结果失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 启动改进版加密货币量化交易策略...")
    print("=" * 60)

    try:
        # 创建改进策略
        strategy = ImprovedCryptoStrategy()

        # 运行改进策略
        success = strategy.run_improved_strategy()

        if success:
            print("\n✅ 改进版策略运行成功！")
            print(f"📁 详细结果请查看: {strategy.output_dir}")
        else:
            print("\n❌ 改进版策略运行失败")

        return success

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n程序执行完成，结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
