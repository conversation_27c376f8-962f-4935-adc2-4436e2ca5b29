基于已获取的507个USDT永续合约每根日K线实体涨跌幅数据，用lightgbm模型，构建一个完整的量化交易回测系统，具体要求如下：

**数据和特征工程：**
1. 使用已计算的507个合约日K线实体涨跌幅数据作为基础
2. 集成Qlib库内置的Alpha360因子集，构建360个技术指标特征
3. 确保所有特征计算严格遵循防未来函数原则（T日特征不能使用T+1日及以后的数据）

**模型训练和预测策略：**
1. 采用滚动训练机制：每个交易日A结束后，使用历史数据训练LightGBM模型
2. 预测目标：预测每个币种在A+2日的K线实体涨跌幅度
3. 模型更新：每日增量更新模型参数，保持模型的时效性
4. 特征选择：结合Alpha360因子和实体涨跌幅历史数据

**交易策略：**
1. 交易时点：在A+2日开盘时执行交易
2. 选币逻辑：根据预测的实体涨跌幅度排序
   - 做多：选择预测涨幅最大的前5个币种
   - 做空：选择预测跌幅最大的后5个币种
3. 交易价格：使用A+2日的开盘价执行交易
4. 持仓管理：每日调仓，持仓期为1天

**回测框架要求：**
1. 使用Qlib框架进行回测，确保数据处理和回测逻辑的一致性
2. 严格的时间序列验证，避免数据泄露
3. 考虑交易成本、滑点等实际交易因素
4. 生成详细的回测报告，包括收益率、夏普比率、最大回撤等关键指标

**输出要求：**
1. 完整的代码实现，包括数据预处理、特征工程、模型训练、策略执行和回测分析
2. 详细的性能分析报告
3. 可视化图表展示策略表现
4. 风险分析和策略优化建议